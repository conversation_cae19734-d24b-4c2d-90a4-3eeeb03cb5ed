/**
 * 增强身份验证系统
 * 基于现有3秒检测机制扩展，提供多层次身份验证策略
 */

import { ref, computed } from "vue";
import { useTouchlessExceptionStore } from "@/stores/touchlessException";

// 验证配置 - 根据训练阶段调整
const VERIFICATION_CONFIG = {
  // 不同阶段的超时配置
  timeouts: {
    login: 8000, // 登录阶段：8秒（允许较长时间）
    introduction: 8000, // 介绍阶段：8秒
    preparation: 5000, // 准备阶段：5秒
    training: 2000, // 训练阶段：2秒（最严格）
    reporting: 5000, // 报告阶段：5秒
  },

  // 连续检测要求
  consecutiveDetections: {
    login: 3, // 登录需要连续3次检测
    introduction: 2, // 介绍需要连续2次检测
    preparation: 3, // 准备需要连续3次检测
    training: 2, // 训练需要连续2次检测
    reporting: 2, // 报告需要连续2次检测
  },

  // 恢复策略配置
  recovery: {
    maxRetries: 3, // 最大重试次数
    retryDelay: 2000, // 重试间隔
    gracePeriod: 10000, // 宽限期（训练中断后的恢复时间）
  },
};

export function useEnhancedIdentityVerification() {
  const exceptionStore = useTouchlessExceptionStore();

  // 验证状态
  const isVerificationActive = ref(false);
  const currentStage = ref("training"); // 当前训练阶段
  const expectedPatientId = ref(null);
  const consecutiveDetections = ref(0);
  const lastDetectionTime = ref(null);
  const verificationTimer = ref(null);
  const retryCount = ref(0);
  const isInGracePeriod = ref(false);
  const gracePeriodTimer = ref(null);

  // 回调函数
  const callbacks = ref({
    onIdentityLost: null,
    onUnauthorizedPerson: null,
    onMultiplePersons: null,
    onIdentityRecovered: null,
    onVerificationFailed: null,
  });

  // 计算属性
  const currentTimeout = computed(() => {
    return (
      VERIFICATION_CONFIG.timeouts[currentStage.value] ||
      VERIFICATION_CONFIG.timeouts.training
    );
  });

  const requiredDetections = computed(() => {
    return VERIFICATION_CONFIG.consecutiveDetections[currentStage.value] || 2;
  });

  const timeSinceLastDetection = computed(() => {
    if (!lastDetectionTime.value) return 0;
    return Date.now() - lastDetectionTime.value;
  });

  const isIdentityLost = computed(() => {
    return timeSinceLastDetection.value > currentTimeout.value;
  });

  /**
   * 启动身份验证
   * @param {string} patientId - 预期的患者ID
   * @param {string} stage - 当前训练阶段
   * @param {Object} options - 回调函数配置
   */
  const startVerification = (patientId, stage = "training", options = {}) => {
    console.log(
      `[EnhancedIdentity] 启动身份验证: 患者=${patientId}, 阶段=${stage}`
    );

    // 停止现有验证
    if (isVerificationActive.value) {
      stopVerification();
    }

    // 初始化状态
    expectedPatientId.value = patientId;
    currentStage.value = stage;
    isVerificationActive.value = true;
    consecutiveDetections.value = 0;
    lastDetectionTime.value = Date.now();
    retryCount.value = 0;
    isInGracePeriod.value = false;

    // 设置回调函数
    callbacks.value = {
      onIdentityLost: options.onIdentityLost || (() => {}),
      onUnauthorizedPerson: options.onUnauthorizedPerson || (() => {}),
      onMultiplePersons: options.onMultiplePersons || (() => {}),
      onIdentityRecovered: options.onIdentityRecovered || (() => {}),
      onVerificationFailed: options.onVerificationFailed || (() => {}),
    };

    // 启动验证定时器
    startVerificationTimer();
  };

  /**
   * 停止身份验证
   */
  const stopVerification = () => {
    if (!isVerificationActive.value) return;

    console.log("[EnhancedIdentity] 停止身份验证");

    // 清理定时器
    clearVerificationTimer();
    clearGracePeriodTimer();

    // 重置状态
    isVerificationActive.value = false;
    expectedPatientId.value = null;
    consecutiveDetections.value = 0;
    lastDetectionTime.value = null;
    retryCount.value = 0;
    isInGracePeriod.value = false;

    // 清空回调
    callbacks.value = {
      onIdentityLost: null,
      onUnauthorizedPerson: null,
      onMultiplePersons: null,
      onIdentityRecovered: null,
      onVerificationFailed: null,
    };
  };

  /**
   * 更新检测结果
   * @param {Array} detectedPersons - 检测到的人员列表
   */
  const updateDetection = (detectedPersons = []) => {
    if (!isVerificationActive.value) return;

    const currentTime = Date.now();

    // 分析检测结果
    const detectionResult = analyzeDetection(detectedPersons);

    switch (detectionResult.type) {
      case "valid":
        handleValidDetection(currentTime);
        break;

      case "unauthorized":
        handleUnauthorizedPerson(detectionResult.data, currentTime);
        break;

      case "multiple":
        handleMultiplePersons(detectionResult.data, currentTime);
        break;

      case "none":
        handleNoDetection(currentTime);
        break;
    }
  };

  /**
   * 分析检测结果
   */
  const analyzeDetection = (detectedPersons) => {
    if (!detectedPersons || detectedPersons.length === 0) {
      return { type: "none" };
    }

    if (detectedPersons.length > 1) {
      return {
        type: "multiple",
        data: {
          count: detectedPersons.length,
          persons: detectedPersons,
        },
      };
    }

    const detectedPerson = detectedPersons[0];

    if (detectedPerson.patient_id === expectedPatientId.value) {
      return {
        type: "valid",
        data: detectedPerson,
      };
    } else {
      return {
        type: "unauthorized",
        data: detectedPerson,
      };
    }
  };

  /**
   * 处理有效检测
   */
  const handleValidDetection = (currentTime) => {
    lastDetectionTime.value = currentTime;
    consecutiveDetections.value++;

    console.log(
      `[EnhancedIdentity] 有效检测: ${consecutiveDetections.value}/${requiredDetections.value}`
    );

    // 如果在宽限期内恢复身份
    if (isInGracePeriod.value) {
      handleIdentityRecovered();
    }

    // 重置重试计数
    if (retryCount.value > 0) {
      retryCount.value = 0;
    }
  };

  /**
   * 处理未授权人员
   */
  const handleUnauthorizedPerson = (person, currentTime) => {
    console.log(`[EnhancedIdentity] 检测到未授权人员: ${person.patient_id}`);

    consecutiveDetections.value = 0;

    exceptionStore.recordIdentityException("unauthorized_person", {
      detectedId: person.patient_id,
      expectedId: expectedPatientId.value,
      confidence: person.confidence,
      timestamp: currentTime,
    });

    callbacks.value.onUnauthorizedPerson?.({
      type: "unauthorized_person",
      detectedPerson: person,
      expectedId: expectedPatientId.value,
    });

    // 启动宽限期
    startGracePeriod();
  };

  /**
   * 处理多人检测
   */
  const handleMultiplePersons = (data, currentTime) => {
    console.log(`[EnhancedIdentity] 检测到多人: ${data.count}人`);

    consecutiveDetections.value = 0;

    exceptionStore.recordIdentityException("multiple_persons", {
      personCount: data.count,
      persons: data.persons,
      expectedId: expectedPatientId.value,
      timestamp: currentTime,
    });

    callbacks.value.onMultiplePersons?.({
      type: "multiple_persons",
      personCount: data.count,
      persons: data.persons,
    });

    // 启动宽限期
    startGracePeriod();
  };

  /**
   * 处理无检测
   */
  const handleNoDetection = (currentTime) => {
    consecutiveDetections.value = 0;

    // 检查是否超时
    if (timeSinceLastDetection.value > currentTimeout.value) {
      handleIdentityLost(currentTime);
    }
  };

  /**
   * 处理身份丢失
   */
  const handleIdentityLost = (currentTime) => {
    console.log(
      `[EnhancedIdentity] 身份验证丢失，超时: ${timeSinceLastDetection.value}ms`
    );

    exceptionStore.recordIdentityException("identity_lost", {
      lastDetectionTime: lastDetectionTime.value,
      timeoutDuration: timeSinceLastDetection.value,
      expectedTimeout: currentTimeout.value,
      stage: currentStage.value,
      timestamp: currentTime,
    });

    callbacks.value.onIdentityLost?.({
      type: "identity_lost",
      timeoutDuration: timeSinceLastDetection.value,
      stage: currentStage.value,
    });

    // 启动宽限期
    startGracePeriod();
  };

  /**
   * 处理身份恢复
   */
  const handleIdentityRecovered = () => {
    console.log("[EnhancedIdentity] 身份验证恢复");

    clearGracePeriodTimer();
    isInGracePeriod.value = false;
    retryCount.value = 0;

    callbacks.value.onIdentityRecovered?.({
      type: "identity_recovered",
      recoveryTime: Date.now(),
    });
  };

  /**
   * 启动验证定时器
   */
  const startVerificationTimer = () => {
    clearVerificationTimer();

    verificationTimer.value = setInterval(() => {
      if (!isVerificationActive.value) return;

      // 检查身份是否丢失
      if (isIdentityLost.value && !isInGracePeriod.value) {
        handleIdentityLost(Date.now());
      }
    }, 1000); // 每秒检查一次
  };

  /**
   * 启动宽限期
   */
  const startGracePeriod = () => {
    if (isInGracePeriod.value) return;

    console.log(
      `[EnhancedIdentity] 启动宽限期: ${VERIFICATION_CONFIG.recovery.gracePeriod}ms`
    );

    isInGracePeriod.value = true;

    gracePeriodTimer.value = setTimeout(() => {
      if (isInGracePeriod.value) {
        handleGracePeriodExpired();
      }
    }, VERIFICATION_CONFIG.recovery.gracePeriod);
  };

  /**
   * 处理宽限期过期
   */
  const handleGracePeriodExpired = () => {
    console.log("[EnhancedIdentity] 宽限期过期，验证失败");

    isInGracePeriod.value = false;
    retryCount.value++;

    if (retryCount.value >= VERIFICATION_CONFIG.recovery.maxRetries) {
      // 达到最大重试次数，验证失败
      callbacks.value.onVerificationFailed?.({
        type: "verification_failed",
        reason: "max_retries_exceeded",
        retryCount: retryCount.value,
      });
    } else {
      // 继续重试
      setTimeout(() => {
        if (isVerificationActive.value) {
          startGracePeriod();
        }
      }, VERIFICATION_CONFIG.recovery.retryDelay);
    }
  };

  /**
   * 更新训练阶段
   */
  const updateStage = (newStage) => {
    if (currentStage.value !== newStage) {
      console.log(
        `[EnhancedIdentity] 更新训练阶段: ${currentStage.value} -> ${newStage}`
      );
      currentStage.value = newStage;

      // 重置连续检测计数
      consecutiveDetections.value = 0;
    }
  };

  /**
   * 清理定时器
   */
  const clearVerificationTimer = () => {
    if (verificationTimer.value) {
      clearInterval(verificationTimer.value);
      verificationTimer.value = null;
    }
  };

  const clearGracePeriodTimer = () => {
    if (gracePeriodTimer.value) {
      clearTimeout(gracePeriodTimer.value);
      gracePeriodTimer.value = null;
    }
  };

  /**
   * 获取验证状态
   */
  const getVerificationStatus = () => {
    return {
      isActive: isVerificationActive.value,
      stage: currentStage.value,
      expectedPatientId: expectedPatientId.value,
      consecutiveDetections: consecutiveDetections.value,
      requiredDetections: requiredDetections.value,
      timeSinceLastDetection: timeSinceLastDetection.value,
      currentTimeout: currentTimeout.value,
      isIdentityLost: isIdentityLost.value,
      isInGracePeriod: isInGracePeriod.value,
      retryCount: retryCount.value,
    };
  };
  /**
   * 更新姿态数据进行身份验证
   * @param {Array} poseKeypoints - 姿态关键点数据
   * @param {string} detectedPatientId - 检测到的患者ID
   */
  const updatePoseData = (poseKeypoints, detectedPatientId) => {
    if (!isVerificationActive.value) return;

    // 模拟从姿态数据中提取的人员信息
    // 在实际实现中，这里应该调用人脸识别或其他身份识别算法
    const detectedPersons = [];

    if (detectedPatientId) {
      detectedPersons.push({
        patient_id: detectedPatientId,
        confidence: 0.9, // 模拟置信度
        source: "pose_analysis",
      });
    }

    // 调用现有的检测更新方法
    updateDetection(detectedPersons);
  };

  /**
   * 更新患者ID
   * @param {string} newPatientId - 新的患者ID
   */
  const updatePatientId = (newPatientId) => {
    if (!isVerificationActive.value) return;

    // 如果患者ID发生变化，重新评估身份验证状态
    if (newPatientId !== expectedPatientId.value) {
      console.log(
        `[EnhancedIdentity] 患者ID变化: ${expectedPatientId.value} -> ${newPatientId}`
      );

      // 如果新ID为空，触发身份丢失
      if (!newPatientId) {
        handleNoDetection(Date.now());
      } else {
        // 如果是不同的患者ID，触发未授权检测
        handleUnauthorizedPerson(
          {
            patient_id: newPatientId,
            confidence: 0.8,
            source: "id_change",
          },
          Date.now()
        );
      }
    }
  };

  return {
    // 状态
    isVerificationActive,
    currentStage,
    expectedPatientId,
    consecutiveDetections,
    isInGracePeriod,
    retryCount,

    // 计算属性
    currentTimeout,
    requiredDetections,
    timeSinceLastDetection,
    isIdentityLost,

    // 方法
    startVerification,
    stopVerification,
    updateDetection,
    updateStage,
    getVerificationStatus,
    updatePoseData,
    updatePatientId,

    // 配置
    VERIFICATION_CONFIG,
  };
}
