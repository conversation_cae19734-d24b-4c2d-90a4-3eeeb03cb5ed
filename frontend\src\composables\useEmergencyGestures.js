/**
 * 紧急手势控制系统
 * 识别基础紧急手势，提供无接触式系统控制
 */

import { ref, computed } from "vue";
import { useTouchlessExceptionStore } from "@/stores/touchlessException";

// 手势配置
const GESTURE_CONFIG = {
  // 手势检测阈值
  detection: {
    confidenceThreshold: 0.7, // 置信度阈值
    holdDuration: 2000, // 手势保持时间（毫秒）
    cooldownPeriod: 3000, // 手势冷却期
    maxDistance: 0.3, // 最大手部移动距离
    stabilityFrames: 10, // 稳定性检测帧数
  },

  // 手势定义
  gestures: {
    stop: {
      name: "停止手势",
      description: "双手举起，掌心向前",
      keypoints: ["left_wrist", "right_wrist", "left_hand", "right_hand"],
      action: "emergency_stop",
    },
    help: {
      name: "求助手势",
      description: "单手举起挥动",
      keypoints: ["left_wrist", "right_wrist"],
      action: "request_help",
    },
    restart: {
      name: "重新开始",
      description: "双手交叉后分开",
      keypoints: ["left_wrist", "right_wrist"],
      action: "restart_training",
    },
  },
};

export function useEmergencyGestures() {
  const exceptionStore = useTouchlessExceptionStore();

  // 检测状态
  const isDetectionActive = ref(false);
  const currentGesture = ref(null);
  const gestureStartTime = ref(null);
  const lastGestureTime = ref(null);
  const gestureHistory = ref([]);
  const stabilityBuffer = ref([]);

  // 回调函数
  const callbacks = ref({
    onEmergencyStop: null,
    onRequestHelp: null,
    onRestartTraining: null,
    onGestureDetected: null,
    onGestureCompleted: null,
  });

  // 计算属性
  const isInCooldown = computed(() => {
    if (!lastGestureTime.value) return false;
    return (
      Date.now() - lastGestureTime.value <
      GESTURE_CONFIG.detection.cooldownPeriod
    );
  });

  const gestureHoldTime = computed(() => {
    if (!gestureStartTime.value) return 0;
    return Date.now() - gestureStartTime.value;
  });

  const isGestureStable = computed(() => {
    return (
      stabilityBuffer.value.length >= GESTURE_CONFIG.detection.stabilityFrames
    );
  });

  /**
   * 启动手势检测
   * @param {Object} options - 回调函数配置
   */
  const startDetection = (options = {}) => {
    console.log("[EmergencyGestures] 启动紧急手势检测");

    isDetectionActive.value = true;
    currentGesture.value = null;
    gestureStartTime.value = null;
    stabilityBuffer.value = [];

    // 设置回调函数
    callbacks.value = {
      onEmergencyStop: options.onEmergencyStop || (() => {}),
      onRequestHelp: options.onRequestHelp || (() => {}),
      onRestartTraining: options.onRestartTraining || (() => {}),
      onGestureDetected: options.onGestureDetected || (() => {}),
      onGestureCompleted: options.onGestureCompleted || (() => {}),
    };
  };

  /**
   * 停止手势检测
   */
  const stopDetection = () => {
    console.log("[EmergencyGestures] 停止紧急手势检测");

    isDetectionActive.value = false;
    currentGesture.value = null;
    gestureStartTime.value = null;
    stabilityBuffer.value = [];

    // 清空回调
    callbacks.value = {
      onEmergencyStop: null,
      onRequestHelp: null,
      onRestartTraining: null,
      onGestureDetected: null,
      onGestureCompleted: null,
    };
  };

  /**
   * 更新姿态数据进行手势检测
   * @param {Object} poseData - 姿态检测数据
   */
  const updatePoseData = (poseData) => {
    if (!isDetectionActive.value || isInCooldown.value) return;

    // 检测手势
    const detectedGesture = detectGesture(poseData);

    if (detectedGesture) {
      handleGestureDetection(detectedGesture, poseData);
    } else {
      // 没有检测到手势，重置状态
      resetGestureState();
    }
  };

  /**
   * 检测手势
   * @param {Object} poseData - 姿态数据
   * @returns {string|null} - 检测到的手势类型
   */
  const detectGesture = (poseData) => {
    if (!poseData || !poseData.keypoints) return null;

    const keypoints = poseData.keypoints;

    // 检测停止手势（双手举起）
    if (detectStopGesture(keypoints)) {
      return "stop";
    }

    // 检测求助手势（单手举起挥动）
    if (detectHelpGesture(keypoints)) {
      return "help";
    }

    // 检测重新开始手势（双手交叉后分开）
    if (detectRestartGesture(keypoints)) {
      return "restart";
    }

    return null;
  };

  /**
   * 检测停止手势
   */
  const detectStopGesture = (keypoints) => {
    const leftWrist = getKeypoint(keypoints, "left_wrist");
    const rightWrist = getKeypoint(keypoints, "right_wrist");
    const nose = getKeypoint(keypoints, "nose");

    if (!leftWrist || !rightWrist || !nose) return false;

    // 检查置信度
    if (
      leftWrist.score < GESTURE_CONFIG.detection.confidenceThreshold ||
      rightWrist.score < GESTURE_CONFIG.detection.confidenceThreshold
    ) {
      return false;
    }

    // 双手都在头部以上
    const leftAboveHead = leftWrist.y < nose.y - 0.1;
    const rightAboveHead = rightWrist.y < nose.y - 0.1;

    // 双手分开一定距离
    const handsDistance = Math.abs(leftWrist.x - rightWrist.x);
    const minDistance = 0.2;

    return leftAboveHead && rightAboveHead && handsDistance > minDistance;
  };

  /**
   * 检测求助手势
   */
  const detectHelpGesture = (keypoints) => {
    const leftWrist = getKeypoint(keypoints, "left_wrist");
    const rightWrist = getKeypoint(keypoints, "right_wrist");
    const nose = getKeypoint(keypoints, "nose");

    if (!nose) return false;

    // 至少一只手在头部以上且有足够置信度
    const leftValid =
      leftWrist &&
      leftWrist.score >= GESTURE_CONFIG.detection.confidenceThreshold;
    const rightValid =
      rightWrist &&
      rightWrist.score >= GESTURE_CONFIG.detection.confidenceThreshold;

    const leftAboveHead = leftValid && leftWrist.y < nose.y - 0.1;
    const rightAboveHead = rightValid && rightWrist.y < nose.y - 0.1;

    // 只有一只手举起（求助手势）
    return (
      (leftAboveHead && !rightAboveHead) || (!leftAboveHead && rightAboveHead)
    );
  };

  /**
   * 检测重新开始手势
   */
  const detectRestartGesture = (keypoints) => {
    const leftWrist = getKeypoint(keypoints, "left_wrist");
    const rightWrist = getKeypoint(keypoints, "right_wrist");

    if (!leftWrist || !rightWrist) return false;

    // 检查置信度
    if (
      leftWrist.score < GESTURE_CONFIG.detection.confidenceThreshold ||
      rightWrist.score < GESTURE_CONFIG.detection.confidenceThreshold
    ) {
      return false;
    }

    // 双手在胸前交叉位置
    const handsDistance = Math.sqrt(
      Math.pow(leftWrist.x - rightWrist.x, 2) +
        Math.pow(leftWrist.y - rightWrist.y, 2)
    );

    // 手部距离很近（交叉状态）
    return handsDistance < 0.15;
  };

  /**
   * 处理手势检测
   */
  const handleGestureDetection = (gestureType, poseData) => {
    const currentTime = Date.now();

    // 如果是新手势
    if (currentGesture.value !== gestureType) {
      console.log(`[EmergencyGestures] 检测到新手势: ${gestureType}`);

      currentGesture.value = gestureType;
      gestureStartTime.value = currentTime;
      stabilityBuffer.value = [];

      callbacks.value.onGestureDetected?.({
        type: gestureType,
        startTime: currentTime,
        config: GESTURE_CONFIG.gestures[gestureType],
      });
    }

    // 添加到稳定性缓冲区
    stabilityBuffer.value.push({
      timestamp: currentTime,
      gestureType: gestureType,
      poseData: poseData,
    });

    // 保持缓冲区大小
    if (
      stabilityBuffer.value.length > GESTURE_CONFIG.detection.stabilityFrames
    ) {
      stabilityBuffer.value.shift();
    }

    // 检查手势是否稳定且持续足够时间
    if (
      isGestureStable.value &&
      gestureHoldTime.value >= GESTURE_CONFIG.detection.holdDuration
    ) {
      handleGestureCompleted(gestureType, currentTime);
    }
  };

  /**
   * 处理手势完成
   */
  const handleGestureCompleted = (gestureType, currentTime) => {
    console.log(`[EmergencyGestures] 手势完成: ${gestureType}`);

    // 记录手势
    const gestureRecord = {
      type: gestureType,
      startTime: gestureStartTime.value,
      completedTime: currentTime,
      holdDuration: gestureHoldTime.value,
      config: GESTURE_CONFIG.gestures[gestureType],
    };

    gestureHistory.value.push(gestureRecord);
    exceptionStore.recordEmergencyGesture(gestureType, gestureRecord);

    // 设置冷却期
    lastGestureTime.value = currentTime;

    // 重置状态
    resetGestureState();

    // 触发相应的回调
    switch (gestureType) {
      case "stop":
        callbacks.value.onEmergencyStop?.({
          type: "emergency_stop",
          gesture: gestureRecord,
        });
        break;

      case "help":
        callbacks.value.onRequestHelp?.({
          type: "request_help",
          gesture: gestureRecord,
        });
        break;

      case "restart":
        callbacks.value.onRestartTraining?.({
          type: "restart_training",
          gesture: gestureRecord,
        });
        break;
    }

    // 通用完成回调
    callbacks.value.onGestureCompleted?.({
      gestureType,
      gesture: gestureRecord,
    });
  };

  /**
   * 重置手势状态
   */
  const resetGestureState = () => {
    currentGesture.value = null;
    gestureStartTime.value = null;
    stabilityBuffer.value = [];
  };

  /**
   * 获取关键点
   */
  const getKeypoint = (keypoints, name) => {
    return keypoints.find((kp) => kp.name === name);
  };

  /**
   * 获取手势统计
   */
  const getGestureStatistics = () => {
    const stats = {
      total: gestureHistory.value.length,
      byType: {},
      recent: gestureHistory.value.filter(
        (g) => Date.now() - g.completedTime < 60000 // 最近1分钟
      ),
    };

    gestureHistory.value.forEach((gesture) => {
      stats.byType[gesture.type] = (stats.byType[gesture.type] || 0) + 1;
    });

    return stats;
  };

  /**
   * 清理手势历史
   */
  const cleanupHistory = (olderThanMinutes = 60) => {
    const cutoffTime = Date.now() - olderThanMinutes * 60 * 1000;
    const initialCount = gestureHistory.value.length;

    gestureHistory.value = gestureHistory.value.filter(
      (gesture) => gesture.completedTime > cutoffTime
    );

    const removedCount = initialCount - gestureHistory.value.length;
    console.log(
      `[EmergencyGestures] 清理手势历史: 移除 ${removedCount} 条记录`
    );
  };

  /**
   * 获取检测状态
   */
  const getDetectionStatus = () => {
    return {
      isActive: isDetectionActive.value,
      currentGesture: currentGesture.value,
      gestureHoldTime: gestureHoldTime.value,
      isInCooldown: isInCooldown.value,
      isGestureStable: isGestureStable.value,
      stabilityFrames: stabilityBuffer.value.length,
      gestureCount: gestureHistory.value.length,
    };
  };

  return {
    // 状态
    isDetectionActive,
    currentGesture,
    gestureHistory,

    // 计算属性
    isInCooldown,
    gestureHoldTime,
    isGestureStable,

    // 方法
    startDetection,
    stopDetection,
    updatePoseData,
    getGestureStatistics,
    cleanupHistory,
    getDetectionStatus,

    // 配置
    GESTURE_CONFIG,
  };
}
