# 无接触异常处理系统

本系统为数字康复训练平台提供完全无接触的异常处理能力，包括智能超时检测、身份验证增强和紧急手势控制。

## 系统组件

### 1. 核心Composables

#### `useTouchlessActionTimeout.js`
- **功能**: 基于时间的智能难度检测
- **特性**: 90秒超时、30秒停滞检测、自动跳过
- **回调**: `onTimeout`, `onWarning`, `onStagnation`

#### `useEnhancedIdentityVerification.js`
- **功能**: 多层身份验证策略
- **特性**: 阶段化超时配置、宽限期恢复
- **回调**: `onIdentityLost`, `onUnauthorizedPerson`, `onMultiplePersons`, `onIdentityRecovered`

#### `useEmergencyGestures.js`
- **功能**: 紧急手势识别
- **特性**: 停止、求助、重启手势检测
- **回调**: `onEmergencyStop`, `onRequestHelp`, `onRestartTraining`

### 2. 状态管理

#### `touchlessException.js` Store
- **功能**: 集中化异常状态管理
- **特性**: 跳过记录、警告追踪、统计信息

### 3. UI组件

#### `TimeoutWarning.vue`
- **功能**: 超时警告显示
- **特性**: 圆形倒计时、进度条、自动隐藏

#### `ActionSkipNotice.vue`
- **功能**: 动作跳过通知
- **特性**: 跳过原因显示、持续时间格式化、自动消失

#### `EmergencyPause.vue`
- **功能**: 紧急暂停覆盖层
- **特性**: 紧急类型分类、状态指示器、自动恢复

## 集成说明

### TrainingView.vue 集成
系统已完全集成到主训练界面：

1. **初始化**: 在 `onMounted` 中启动所有检测系统
2. **数据流**: 姿态数据自动传递给各检测系统
3. **UI显示**: 异常UI组件覆盖在训练界面上
4. **清理**: 在 `onUnmounted` 中清理所有资源

### useEnhancedTrainingSession.js 集成
训练会话管理已集成超时检测：

1. **启动检测**: 每个动作开始时自动启动超时检测
2. **进度更新**: 评估引擎分数变化时更新超时进展
3. **自动跳过**: 超时时自动跳过动作并显示通知
4. **手动控制**: 提供手动跳过和重启方法

## 使用方法

### 开发环境测试

在浏览器控制台中运行：

```javascript
// 运行完整集成测试
await window.runTouchlessTest()

// 手动测试各个系统
const systems = window.touchlessTestSystems
systems.touchlessTimeout.startDetection(mockAction, callbacks)
systems.emergencyGestures.startDetection(callbacks)
systems.enhancedIdentity.startVerification(patientId, stage, callbacks)
```

### 生产环境配置

系统默认配置：

```javascript
// 超时检测配置
const timeoutConfig = {
  totalTimeout: 90000,      // 90秒总超时
  stagnationTimeout: 30000, // 30秒停滞检测
  warningTime: 15000        // 15秒警告时间
}

// 身份验证配置
const identityConfig = {
  login: { timeout: 8000, gracePeriod: 2000 },
  training: { timeout: 2000, gracePeriod: 1000 }
}

// 手势检测配置
const gestureConfig = {
  confidenceThreshold: 0.7,
  stabilityFrames: 5,
  cooldownPeriod: 3000
}
```

## 异常处理流程

### 1. 超时异常
1. 动作开始 → 启动超时检测
2. 75秒 → 显示超时警告
3. 90秒 → 自动跳过动作
4. 显示跳过通知 → 继续下一动作

### 2. 身份异常
1. 检测到身份问题 → 显示紧急暂停
2. 暂停训练 → 等待身份恢复
3. 身份恢复 → 自动恢复训练

### 3. 紧急手势
1. 检测到紧急手势 → 立即响应
2. 显示紧急暂停界面 → 执行相应操作
3. 根据手势类型决定后续流程

## 配置选项

### 超时检测自定义
```javascript
touchlessTimeout.configure({
  totalTimeout: 120000,     // 自定义总超时时间
  warningRatio: 0.8,        // 警告时间比例
  stagnationSensitivity: 0.1 // 停滞检测敏感度
})
```

### 身份验证自定义
```javascript
enhancedIdentity.configure({
  stages: {
    training: { timeout: 3000, gracePeriod: 1500 }
  },
  retryLimit: 3
})
```

### 手势检测自定义
```javascript
emergencyGestures.configure({
  gestures: {
    stop: { enabled: true, sensitivity: 0.8 },
    help: { enabled: true, sensitivity: 0.7 },
    restart: { enabled: false }
  }
})
```

## 故障排除

### 常见问题

1. **超时检测不工作**
   - 检查 `isDetectionActive` 状态
   - 确认回调函数正确设置
   - 验证动作数据格式

2. **身份验证失效**
   - 检查患者ID是否正确传递
   - 确认姿态数据流是否正常
   - 验证阶段配置

3. **手势识别不准确**
   - 调整置信度阈值
   - 增加稳定性帧数
   - 检查姿态关键点质量

### 调试工具

```javascript
// 启用详细日志
window.touchlessTestSystems.touchlessTimeout.enableDebugMode()
window.touchlessTestSystems.enhancedIdentity.enableDebugMode()
window.touchlessTestSystems.emergencyGestures.enableDebugMode()

// 查看当前状态
console.log(window.touchlessTestSystems.touchlessExceptionStore.getStatistics())
```

## 性能考虑

- 姿态数据处理频率：30-60 FPS
- 内存使用：约 2-5MB 额外开销
- CPU 影响：< 5% 在现代设备上
- 网络影响：无（完全本地处理）

## 未来扩展

计划中的功能：
- 语音命令集成
- 眼动追踪支持
- 自适应难度调整
- 个性化手势训练
- 多模态融合检测