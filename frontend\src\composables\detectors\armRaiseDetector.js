/**
 * 手臂上举动作检测器
 * 检测用户将手臂从体侧向上举起的动作
 */

import { utils } from '@/utils/geometryUtils'
import { useTrainingReportStore } from '@/stores/trainingReport'

export class ArmRaiseDetector {
  constructor(side = 'left', level = 'medium') {
    this.side = side
    this.level = level
    
    // 根据侧别设置关键点索引
    if (this.side === 'left') {
      this.wristIdx = 9  // LEFT_WRIST
      this.elbowIdx = 7  // LEFT_ELBOW
      this.shoulderIdx = 5  // LEFT_SHOULDER
      this.kneeIdx = 13  // LEFT_KNEE (用于返回判定)
      this.hipIdx = 11   // LEFT_HIP (用于返回判定)
    } else {
      this.wristIdx = 10  // RIGHT_WRIST
      this.elbowIdx = 8   // RIGHT_ELBOW
      this.shoulderIdx = 6   // RIGHT_SHOULDER
      this.kneeIdx = 14  // RIGHT_KNEE (用于返回判定)
      this.hipIdx = 12   // RIGHT_HIP (用于返回判定)
    }
    
    // 根据难度设置参数
    this.config = this.getDifficultyConfig(level)

    // 初始化报告存储
    this.reportStore = useTrainingReportStore()

    // 初始化状态
    this.reset()
  }
  
  getDifficultyConfig(level) {
    switch (level) {
      case 'easy':
        return {
          targetElbowAngle: 140,    // 目标肘关节角度（度）- 提高要求
          targetRaiseAngle: 90,     // 目标抬起角度（度）- 提高要求
          holdDuration: 1000,       // 保持时间（毫秒）- 增加保持时间
          angleThreshold: 10,        // 角度容差 - 减小容差提高精度
          stabilityFrames: 10       // 稳定性检查帧数
        }
      case 'hard':
        return {
          targetElbowAngle: 165,    // 适中要求
          targetRaiseAngle: 85,     // 适中要求
          holdDuration: 3000,       // 适中保持时间
          angleThreshold: 8,        // 适中容差
          stabilityFrames: 10       // 适中稳定性检查帧数
        }
      case 'medium':
      default:
        return {
          targetElbowAngle: 160,    // 适中要求
          targetRaiseAngle: 80,     // 适中要求
          holdDuration: 2000,       // 适中保持时间
          angleThreshold: 10,       // 适中容差
          stabilityFrames: 8        // 稳定性检查帧数
        }
    }
  }
  
  reset() {
    this.state = 'IDLE'
    this.score = 0
    this.feedback = `准备将${this.side === 'left' ? '左' : '右'}手臂向上举起`

    // 内部状态变量
    this.holdStartTime = 0
    this.maxElbowAngleReached = 0
    this.maxRaiseAngleReached = 0
    this.initialElbowAngle = 0
    this.initialRaiseAngle = 0

    // 稳定性检查变量
    this.stableFrameCount = 0        // 稳定帧计数
    this.lastElbowAngle = 0          // 上一帧肘关节角度
    this.lastRaiseAngle = 0          // 上一帧抬起角度
    this.angleStabilityThreshold = 5 // 角度稳定性阈值（度）

    // 分阶段得分记录
    this.raiseScore = 0      // 抬起阶段得分 (0-40)
    this.holdScore = 0       // 保持阶段得分 (0-30)
    this.lowerScore = 0      // 放下阶段得分 (0-30)
  }
  
  /**
   * 更新检测器状态
   * @param {Array} keypoints - 关键点数据
   * @returns {Object} 检测结果
   */
  update(keypoints) {
    if (this.state === 'COMPLETED') {
      return { 
        success: true,
        state: this.state, 
        score: this.score, 
        feedback: this.feedback 
      }
    }
    // 数据提取和验证
    const getPoint = (idx) => ({ 
      x: keypoints[idx][0], 
      y: keypoints[idx][1], 
      c: keypoints[idx][2] 
    })
    const wrist = getPoint(this.wristIdx)
    const elbow = getPoint(this.elbowIdx)
    const shoulder = getPoint(this.shoulderIdx)
    if (utils.multipleOutOfBounds([wrist, elbow, shoulder])) {
      return {
        success: false,
        state: this.state,
        score: this.score,
        feedback: "请保证手臂关键部位在画面中",
      };
    }
    // 计算肘关节角度（肩膀-肘部-手腕的角度）
    const elbowAngle = utils.calculateAngle(shoulder, elbow, wrist)

    // 计算手臂相对于身体的抬起角度（肩膀到手腕的向量与水平线的夹角）
    const shoulderToWrist = { x: wrist.x - shoulder.x, y: wrist.y - shoulder.y }
    const raiseAngle = Math.abs(Math.atan2(-shoulderToWrist.y, Math.abs(shoulderToWrist.x)) * 180 / Math.PI)

    // 更新最大角度记录
    this.maxElbowAngleReached = Math.max(this.maxElbowAngleReached, elbowAngle)
    this.maxRaiseAngleReached = Math.max(this.maxRaiseAngleReached, raiseAngle)
    
    // 状态机逻辑
    switch (this.state) {
      case 'IDLE':
        // 检测开始抬起：关键条件是肘部必须高于肩部
        const elbowAboveShoulder = elbow.y < shoulder.y  // Y轴向下为正，所以肘部Y值小于肩部表示肘部更高
        const hasBasicAngle = elbowAngle > 110 || raiseAngle > 10

        // 必须同时满足：1) 肘部高于肩部 2) 基本的角度条件
        if (elbowAboveShoulder && hasBasicAngle) {
          this.state = 'RAISING'
          this.initialElbowAngle = elbowAngle
          this.initialRaiseAngle = raiseAngle
          this.feedback = '很好，继续向上举起手臂...'
          console.log(`[ArmRaise] 开始检测到抬起动作 - 肘关节: ${Math.round(elbowAngle)}°, 抬起: ${Math.round(raiseAngle)}°, 肘部高于肩部: ${elbowAboveShoulder}`)
        } else if (hasBasicAngle && !elbowAboveShoulder) {
          this.feedback = '请将手臂向上抬起，肘部要高于肩部'
        }
        break

      case 'RAISING':
        // 首先检查基本条件：肘部必须始终高于肩部
        const elbowStillAboveShoulder = elbow.y < shoulder.y
        if (!elbowStillAboveShoulder) {
          this.state = 'IDLE'
          this.stableFrameCount = 0
          this.feedback = '手臂放下了，请重新将肘部抬高到肩部以上'
          console.log(`[ArmRaise] 肘部不再高于肩部，返回IDLE状态`)
          break
        }

        // 抬起阶段评分 (0-40分) - 改进的渐进式评分
        const elbowProgress = Math.min(1, Math.max(0, (elbowAngle - 120) / (this.config.targetElbowAngle - 120)))
        const raiseProgress = Math.min(1, Math.max(0, raiseAngle / this.config.targetRaiseAngle))
        const combinedProgress = (elbowProgress + raiseProgress) / 2

        // 渐进式评分：不立即给满分，基于实际达到的程度
        this.raiseScore = Math.max(0, Math.min(35, 35 * combinedProgress)) // 最高35分，留5分给稳定性
        this.score = this.raiseScore + this.holdScore + this.lowerScore
        this.feedback = `继续举起... 肘关节: ${Math.round(elbowAngle)}°, 抬起: ${Math.round(raiseAngle)}°`

        // 检查是否达到目标角度
        const elbowReached = elbowAngle >= this.config.targetElbowAngle - this.config.angleThreshold
        const raiseReached = raiseAngle >= this.config.targetRaiseAngle - this.config.angleThreshold

        if (elbowReached && raiseReached) {
          // 稳定性检查：检查角度是否稳定
          const elbowStable = Math.abs(elbowAngle - this.lastElbowAngle) <= this.angleStabilityThreshold
          const raiseStable = Math.abs(raiseAngle - this.lastRaiseAngle) <= this.angleStabilityThreshold

          if (elbowStable && raiseStable) {
            this.stableFrameCount++
            this.feedback = `很好！保持稳定... (${this.stableFrameCount}/${this.config.stabilityFrames})`

            // 只有稳定足够帧数后才进入HOLDING阶段
            if (this.stableFrameCount >= this.config.stabilityFrames) {
              this.state = 'HOLDING'
              this.holdStartTime = Date.now()
              this.raiseScore = 40  // 抬起阶段满分（包含稳定性奖励）
              this.score = this.raiseScore + this.holdScore + this.lowerScore
              this.feedback = `太棒了！请保持 ${this.config.holdDuration / 1000} 秒`

              // 记录抬起阶段完成
              this.reportStore.recordStageScore('raising', this.raiseScore, 40, '手臂抬起阶段完成（含稳定性验证）')
            }
          } else {
            // 不稳定时重置计数
            this.stableFrameCount = 0
            this.feedback = `接近目标！请保持稳定...`
          }
        } else {
          // 未达到目标角度时重置稳定性计数
          this.stableFrameCount = 0
        }

        // 更新上一帧角度记录
        this.lastElbowAngle = elbowAngle
        this.lastRaiseAngle = raiseAngle
        break
        
      case 'HOLDING':
        // 保持阶段评分 (0-30分)
        const holdingTime = Date.now() - this.holdStartTime

        // 首先检查基本条件：肘部必须始终高于肩部
        const elbowAboveShoulderInHolding = elbow.y < shoulder.y
        if (!elbowAboveShoulderInHolding) {
          this.state = 'RAISING'
          this.stableFrameCount = 0
          this.feedback = '手臂位置下降，请重新抬高肘部到肩部以上'
          console.log(`[ArmRaise] HOLDING阶段肘部不再高于肩部，返回RAISING状态`)
          break
        }

        // 更严格的位置检查：使用更小的容差
        const elbowStillReached = elbowAngle >= this.config.targetElbowAngle - this.config.angleThreshold
        const raiseStillReached = raiseAngle >= this.config.targetRaiseAngle - this.config.angleThreshold

        if (!elbowStillReached || !raiseStillReached) {
          this.state = 'RAISING'
          this.stableFrameCount = 0  // 重置稳定性计数
          this.feedback = '位置偏移！请重新举起手臂到目标位置'
          console.log(`[ArmRaise] 位置检查失败 - 肘关节: ${elbowAngle}°(需要${this.config.targetElbowAngle - this.config.angleThreshold}°), 抬起: ${raiseAngle}°(需要${this.config.targetRaiseAngle - this.config.angleThreshold}°)`)
          break
        }

        // 稳定性检查：在保持阶段也要检查稳定性
        const elbowStable = Math.abs(elbowAngle - this.lastElbowAngle) <= this.angleStabilityThreshold
        const raiseStable = Math.abs(raiseAngle - this.lastRaiseAngle) <= this.angleStabilityThreshold

        if (!elbowStable || !raiseStable) {
          this.feedback = `保持稳定... 检测到轻微抖动`
        }

        // 根据保持时间和稳定性计算分数
        const holdProgress = Math.min(1, holdingTime / this.config.holdDuration)
        const stabilityBonus = (elbowStable && raiseStable) ? 1.0 : 0.8  // 稳定性奖励
        this.holdScore = Math.max(0, Math.min(30, 30 * holdProgress * stabilityBonus))
        this.score = this.raiseScore + this.holdScore + this.lowerScore

        if (holdProgress >= 1) {
          this.state = 'LOWERING'
          this.holdScore = 30  // 保持阶段满分
          this.score = this.raiseScore + this.holdScore + this.lowerScore
          this.feedback = '很好！现在缓慢放下手臂'

          // 记录保持阶段完成
          this.reportStore.recordStageScore('holding', this.holdScore, 30, '手臂保持阶段完成')
        } else {
          this.feedback = `保持... 剩余 ${Math.ceil((this.config.holdDuration - holdingTime) / 1000)} 秒`
        }

        // 更新上一帧角度记录
        this.lastElbowAngle = elbowAngle
        this.lastRaiseAngle = raiseAngle
        break
        
      case 'LOWERING':
        // 获取关键点位置
        const wrist = keypoints[this.wristIdx]
        const knee = keypoints[this.kneeIdx]
        const hip = keypoints[this.hipIdx]

        // 放下阶段评分 (0-30分) - 简化的距离判断
        const wristToKneeDistance = utils.calculateDistance(wrist, knee)
        const wristToHipDistance = utils.calculateDistance(wrist, hip)
        const shoulderToKneeDistance = utils.calculateDistance(shoulder, knee)

        // 基于手腕到腿部的距离计算进度
        const kneeProgress = Math.min(1, Math.max(0, (shoulderToKneeDistance - wristToKneeDistance) / shoulderToKneeDistance))
        this.lowerScore = Math.max(0, Math.min(30, 30 * kneeProgress))
        this.score = this.raiseScore + this.holdScore + this.lowerScore
        this.feedback = `正在放下手臂... 距离腿部: ${Math.round(wristToKneeDistance)}px`

        // 简化的完成判定：手腕距离膝盖或髋部足够近即可（宽松判定）
        const kneeThreshold = shoulderToKneeDistance * 0.6  // 宽松的60%阈值
        const hipThreshold = utils.calculateDistance(shoulder, hip) * 0.7  // 髋部阈值稍微宽松一些

        const isNearKnee = wristToKneeDistance < kneeThreshold
        const isNearHip = wristToHipDistance < hipThreshold

        if (isNearKnee || isNearHip) {
          this.state = 'COMPLETED'
          this.lowerScore = Math.max(this.lowerScore, 25)  // 给予合理的基础分数
          this.score = this.raiseScore + this.holdScore + this.lowerScore
          this.feedback = `动作完成！最终得分: ${Math.round(this.score)}`

          console.log(`[ArmRaise] 动作完成 - 手腕到膝盖: ${Math.round(wristToKneeDistance)}px(阈值${Math.round(kneeThreshold)}px), 手腕到髋部: ${Math.round(wristToHipDistance)}px(阈值${Math.round(hipThreshold)}px)`)

          // 记录放下阶段完成
          this.reportStore.recordStageScore('lowering', this.lowerScore, 30, '手臂放下阶段完成')
        }
        break
    }
    
    // 记录实时反馈
    this.reportStore.recordFeedback(this.feedback, Math.round(this.score), this.state)

    return {
      success: true,
      state: this.state,
      score: Math.round(this.score),
      feedback: this.feedback
    }
  }
}

// 默认导出
export default ArmRaiseDetector
