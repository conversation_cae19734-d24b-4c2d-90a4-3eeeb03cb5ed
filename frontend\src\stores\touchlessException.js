/**
 * 无接触式异常处理状态管理
 * 管理超时、跳过、身份验证异常等状态
 */

import { defineStore } from "pinia";
import { ref, computed } from "vue";

export const useTouchlessExceptionStore = defineStore(
  "touchlessException",
  () => {
    // 当前异常状态
    const currentException = ref(null);
    const isExceptionActive = ref(false);
    const exceptionStartTime = ref(null);

    // 跳过动作记录
    const skippedActions = ref([]);
    const timeoutWarnings = ref([]);
    const detectionHistory = ref([]);

    // 身份验证异常
    const identityExceptions = ref([]);
    const lastIdentityCheck = ref(null);

    // 紧急手势状态
    const emergencyGestures = ref([]);
    const lastEmergencyGesture = ref(null);

    // 配置
    const autoSkipEnabled = ref(true);
    const maxSkipCount = ref(3); // 最大连续跳过数量

    // 计算属性
    const totalSkippedCount = computed(() => skippedActions.value.length);

    const recentSkips = computed(() => {
      const oneHourAgo = Date.now() - 60 * 60 * 1000;
      return skippedActions.value.filter((skip) => skip.timestamp > oneHourAgo);
    });

    const hasRecentWarnings = computed(() => {
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
      return timeoutWarnings.value.some(
        (warning) => warning.timestamp > fiveMinutesAgo
      );
    });

    const isSkipLimitReached = computed(() => {
      return recentSkips.value.length >= maxSkipCount.value;
    });

    /**
     * 记录检测开始
     */
    const recordDetectionStart = (action) => {
      const record = {
        id: generateId(),
        actionId: action.action_id,
        actionType: action.action_type,
        actionName: action.action_name,
        startTime: Date.now(),
        status: "active",
      };

      detectionHistory.value.push(record);
      console.log(`[TouchlessException] 记录检测开始: ${action.action_name}`);
    };

    /**
     * 记录动作跳过
     */
    const recordSkip = (action, reason, metadata = {}) => {
      const skipRecord = {
        id: generateId(),
        actionId: action.action_id,
        actionType: action.action_type,
        actionName: action.action_name,
        skipTime: Date.now(),
        reason: reason, // 'timeout', 'stagnation', 'manual', 'repeated_failure'
        attemptDuration: metadata.elapsedTime || 0,
        stagnationTime: metadata.stagnationTime || 0,
        failureCount: metadata.failureCount || 0,
        progressMade: metadata.progressMade || false,
        metadata: metadata,
      };

      skippedActions.value.push(skipRecord);

      // 更新检测历史
      const detectionRecord = detectionHistory.value.find(
        (record) =>
          record.actionId === action.action_id && record.status === "active"
      );
      if (detectionRecord) {
        detectionRecord.status = "skipped";
        detectionRecord.endTime = Date.now();
        detectionRecord.skipReason = reason;
      }

      console.log(
        `[TouchlessException] 记录动作跳过: ${action.action_name}, 原因: ${reason}`
      );

      // 触发跳过事件
      setCurrentException("action_skipped", {
        action,
        reason,
        skipRecord,
      });
    };

    /**
     * 记录超时警告
     */
    const recordWarning = (action, warningType, metadata = {}) => {
      const warning = {
        id: generateId(),
        actionId: action.action_id,
        actionType: action.action_type,
        actionName: action.action_name,
        timestamp: Date.now(),
        warningType: warningType, // 'timeout_warning', 'stagnation', 'identity_lost'
        metadata: metadata,
      };

      timeoutWarnings.value.push(warning);
      console.log(
        `[TouchlessException] 记录警告: ${warningType}, 动作: ${action.action_name}`
      );

      // 设置当前异常状态
      setCurrentException(warningType, {
        action,
        warning,
        metadata,
      });
    };

    /**
     * 记录身份验证异常
     */
    const recordIdentityException = (exceptionType, metadata = {}) => {
      const exception = {
        id: generateId(),
        timestamp: Date.now(),
        type: exceptionType, // 'identity_lost', 'unauthorized_person', 'multiple_persons'
        metadata: metadata,
      };

      identityExceptions.value.push(exception);
      lastIdentityCheck.value = Date.now();

      console.log(`[TouchlessException] 记录身份验证异常: ${exceptionType}`);

      setCurrentException("identity_exception", {
        exceptionType,
        exception,
        metadata,
      });
    };

    /**
     * 记录紧急手势
     */
    const recordEmergencyGesture = (gestureType, metadata = {}) => {
      const gesture = {
        id: generateId(),
        timestamp: Date.now(),
        type: gestureType, // 'stop', 'help', 'restart'
        metadata: metadata,
      };

      emergencyGestures.value.push(gesture);
      lastEmergencyGesture.value = gesture;

      console.log(`[TouchlessException] 记录紧急手势: ${gestureType}`);

      setCurrentException("emergency_gesture", {
        gestureType,
        gesture,
        metadata,
      });
    };

    /**
     * 设置当前异常状态
     */
    const setCurrentException = (exceptionType, data = {}) => {
      currentException.value = {
        type: exceptionType,
        timestamp: Date.now(),
        data: data,
      };

      isExceptionActive.value = true;
      exceptionStartTime.value = Date.now();

      console.log(`[TouchlessException] 设置当前异常: ${exceptionType}`);
    };

    /**
     * 清除当前异常
     */
    const clearCurrentException = () => {
      if (currentException.value) {
        console.log(
          `[TouchlessException] 清除异常: ${currentException.value.type}`
        );
      }

      currentException.value = null;
      isExceptionActive.value = false;
      exceptionStartTime.value = null;
    };

    /**
     * 获取动作跳过统计
     */
    const getSkipStatistics = () => {
      const stats = {
        total: totalSkippedCount.value,
        recent: recentSkips.value.length,
        byReason: {},
        byActionType: {},
      };

      // 按原因统计
      skippedActions.value.forEach((skip) => {
        stats.byReason[skip.reason] = (stats.byReason[skip.reason] || 0) + 1;
        stats.byActionType[skip.actionType] =
          (stats.byActionType[skip.actionType] || 0) + 1;
      });

      return stats;
    };

    /**
     * 获取警告统计
     */
    const getWarningStatistics = () => {
      const stats = {
        total: timeoutWarnings.value.length,
        recent: hasRecentWarnings.value,
        byType: {},
      };

      timeoutWarnings.value.forEach((warning) => {
        stats.byType[warning.warningType] =
          (stats.byType[warning.warningType] || 0) + 1;
      });

      return stats;
    };

    /**
     * 清理历史记录
     */
    const cleanupHistory = (olderThanHours = 24) => {
      const cutoffTime = Date.now() - olderThanHours * 60 * 60 * 1000;

      const initialCounts = {
        skips: skippedActions.value.length,
        warnings: timeoutWarnings.value.length,
        detections: detectionHistory.value.length,
        identity: identityExceptions.value.length,
        gestures: emergencyGestures.value.length,
      };

      skippedActions.value = skippedActions.value.filter(
        (skip) => skip.skipTime > cutoffTime
      );
      timeoutWarnings.value = timeoutWarnings.value.filter(
        (warning) => warning.timestamp > cutoffTime
      );
      detectionHistory.value = detectionHistory.value.filter(
        (record) => record.startTime > cutoffTime
      );
      identityExceptions.value = identityExceptions.value.filter(
        (exception) => exception.timestamp > cutoffTime
      );
      emergencyGestures.value = emergencyGestures.value.filter(
        (gesture) => gesture.timestamp > cutoffTime
      );

      const finalCounts = {
        skips: skippedActions.value.length,
        warnings: timeoutWarnings.value.length,
        detections: detectionHistory.value.length,
        identity: identityExceptions.value.length,
        gestures: emergencyGestures.value.length,
      };

      console.log("[TouchlessException] 清理历史记录完成:", {
        removed: {
          skips: initialCounts.skips - finalCounts.skips,
          warnings: initialCounts.warnings - finalCounts.warnings,
          detections: initialCounts.detections - finalCounts.detections,
          identity: initialCounts.identity - finalCounts.identity,
          gestures: initialCounts.gestures - finalCounts.gestures,
        },
        remaining: finalCounts,
      });
    };

    /**
     * 重置所有状态
     */
    const resetAll = () => {
      currentException.value = null;
      isExceptionActive.value = false;
      exceptionStartTime.value = null;
      skippedActions.value = [];
      timeoutWarnings.value = [];
      detectionHistory.value = [];
      identityExceptions.value = [];
      emergencyGestures.value = [];
      lastIdentityCheck.value = null;
      lastEmergencyGesture.value = null;

      console.log("[TouchlessException] 重置所有异常状态");
    };

    /**
     * 生成唯一ID
     */
    const generateId = () => {
      return Date.now().toString(36) + Math.random().toString(36).substr(2);
    };

    return {
      // 状态
      currentException,
      isExceptionActive,
      exceptionStartTime,
      skippedActions,
      timeoutWarnings,
      detectionHistory,
      identityExceptions,
      emergencyGestures,
      lastIdentityCheck,
      lastEmergencyGesture,
      autoSkipEnabled,
      maxSkipCount,

      // 计算属性
      totalSkippedCount,
      recentSkips,
      hasRecentWarnings,
      isSkipLimitReached,

      // 方法
      recordDetectionStart,
      recordSkip,
      recordWarning,
      recordIdentityException,
      recordEmergencyGesture,
      setCurrentException,
      clearCurrentException,
      getSkipStatistics,
      getWarningStatistics,
      cleanupHistory,
      resetAll,
    };
  }
);
