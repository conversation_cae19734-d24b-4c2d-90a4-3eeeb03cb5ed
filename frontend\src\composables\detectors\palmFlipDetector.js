/**
 * 手掌翻转动作检测器
 * 检测用户手掌从掌心向上翻转到掌心向下（或相反）的动作
 */

import { utils } from '@/utils/geometryUtils'
import { useTrainingReportStore } from '@/stores/trainingReport'

export class PalmFlipDetector {
  constructor(side = 'left', level = 'medium') {
    this.side = side
    this.level = level
    
    // 设置关键点索引
    this.setupKeypoints()
    
    // 根据难度设置参数
    this.config = this.getDifficultyConfig(level)

    // 初始化报告存储
    this.reportStore = useTrainingReportStore()

    // 初始化状态
    this.reset()
  }
  
  setupKeypoints() {
    if (this.side === 'left') {
      this.wristIdx = 9           // LEFT_WRIST
      this.thumbTipIdx = 95       // LEFT_HAND_THUMB_4
      this.indexTipIdx = 99       // LEFT_HAND_INDEX_4
      this.middleTipIdx = 103     // LEFT_HAND_MIDDLE_4
      this.ringTipIdx = 107       // LEFT_HAND_RING_4
      this.pinkyTipIdx = 111      // LEFT_HAND_PINKY_4
      this.thumbBaseIdx = 92      // LEFT_HAND_THUMB_1
      this.indexBaseIdx = 96      // LEFT_HAND_INDEX_1
    } else {
      this.wristIdx = 10          // RIGHT_WRIST
      this.thumbTipIdx = 116      // RIGHT_HAND_THUMB_4
      this.indexTipIdx = 120      // RIGHT_HAND_INDEX_4
      this.middleTipIdx = 124     // RIGHT_HAND_MIDDLE_4
      this.ringTipIdx = 128       // RIGHT_HAND_RING_4
      this.pinkyTipIdx = 132      // RIGHT_HAND_PINKY_4
      this.thumbBaseIdx = 113     // RIGHT_HAND_THUMB_1
      this.indexBaseIdx = 117     // RIGHT_HAND_INDEX_1
    }
  }
  
  getDifficultyConfig(level) {
    switch (level) {
      case 'easy':
        return {
          holdDuration: 2000,      // 保持时间（毫秒）- 减少时间
          stabilityFrames: 15      // 需要连续稳定的帧数 - 减少帧数
        }
      case 'hard':
        return {
          holdDuration: 3000,      // 增加时间
          stabilityFrames: 20      // 增加稳定帧数
        }
      case 'medium':
      default:
        return {
          holdDuration: 2500,      // 增加时间
          stabilityFrames: 18      // 增加稳定帧数
        }
    }
  }
  
  reset() {
    this.state = 'IDLE'
    this.score = 0
    this.feedback = `准备${this.side === 'left' ? '左' : '右'}手掌翻转动作，请保持手掌向上`

    // 内部状态变量
    this.holdStartTime = 0
    this.isPalmUp = true           // 默认初始状态为手掌向上
    this.flipDetected = false      // 是否检测到翻转
    this.stableFrameCount = 0      // 稳定状态帧计数
    this.lastThumbPosition = null  // 上一帧拇指位置

    // 分阶段得分记录
    this.prepareScore = 0     // 准备阶段得分 (0-20)
    this.flipDownScore = 0    // 向下翻转阶段得分 (0-25)
    this.holdScore = 0        // 保持阶段得分 (0-25)
    this.flipBackScore = 0    // 翻转回来阶段得分 (0-25)
    this.finalScore = 0       // 最终确认阶段得分 (0-5)
  }
  
  /**
   * 基于拇指位置判断手掌朝向
   * 通过分析拇指相对于食指的X轴位置来判断手掌朝向
   * @param {Object} thumbTip - 拇指尖位置
   * @param {Object} indexTip - 食指尖位置
   * @returns {Boolean} true表示手掌向上，false表示手掌向下，null表示无法判断
   */
  isPalmUpByThumbPosition(thumbTip, indexTip) {
    // 检查关键点可见性
    if (thumbTip.c < 0.6 || indexTip.c < 0.6) {
      return null // 关键点不够清晰，无法判断
    }

    // 修正后的手掌朝向判断逻辑
    if (this.side === 'left') {
      // 左手：掌心向上时拇指在食指右边（画面中），掌心向下时拇指在食指左边（画面中）
      return thumbTip.x > indexTip.x
    } else {
      // 右手：掌心向上时拇指在食指左边（画面中），掌心向下时拇指在食指右边（画面中）
      return thumbTip.x < indexTip.x
    }
  }
  
  /**
   * 更新检测器状态
   * @param {Array} keypoints - 关键点数据
   * @returns {Object} 检测结果
   */
  update(keypoints) {
    if (this.state === 'COMPLETED') {
      return { 
        success: true,
        state: this.state, 
        score: this.score, 
        feedback: this.feedback 
      }
    }
    
    // 数据提取和验证
    const getPoint = (idx) => ({ 
      x: keypoints[idx][0], 
      y: keypoints[idx][1], 
      c: keypoints[idx][2] 
    })
    
    const wrist = getPoint(this.wristIdx)
    const thumbTip = getPoint(this.thumbTipIdx)
    const indexTip = getPoint(this.indexTipIdx)

    // 检查关键点是否在画面中
    if (utils.multipleOutOfBounds([wrist, thumbTip, indexTip])) {
      return {
        success: false,
        state: this.state,
        score: this.score,
        feedback: `请保证${this.side === 'left' ? '左' : '右'}手在画面中`,
      }
    }
    
    // 判断当前手掌朝向
    const currentPalmUp = this.isPalmUpByThumbPosition(thumbTip, indexTip)

    // 状态机逻辑
    switch (this.state) {
      case 'IDLE':
        // 等待检测到稳定的手掌向上状态
        if (currentPalmUp === true) {
          this.stableFrameCount++
          if (this.stableFrameCount >= this.config.stabilityFrames) {
            this.state = 'PALM_UP_READY'
            this.prepareScore = 20  // 准备阶段完成
            this.score = this.prepareScore + this.flipDownScore + this.holdScore + this.flipBackScore + this.finalScore
            this.feedback = `检测到手掌向上，请开始翻转到掌心向下`

            // 记录准备阶段完成
            this.reportStore.recordStageScore('preparing', this.prepareScore, 20, '手掌向上准备阶段完成')
          } else {
            this.feedback = `请保持手掌向上... ${this.stableFrameCount}/${this.config.stabilityFrames}`
          }
        } else {
          this.stableFrameCount = 0
          this.feedback = `请调整手势，确保手掌向上（拇指在${this.side === 'left' ? '食指右边' : '食指左边'}）`
        }
        break

      case 'PALM_UP_READY':
        // 等待翻转到掌心向下
        if (currentPalmUp === false) {
          this.state = 'PALM_DOWN_DETECTED'
          this.flipDetected = true
          this.flipDownScore = 25  // 向下翻转阶段完成
          this.score = this.prepareScore + this.flipDownScore + this.holdScore + this.flipBackScore + this.finalScore
          this.feedback = `翻转成功！检测到掌心向下，请保持姿势`
          this.stableFrameCount = 0

          // 记录翻转阶段完成
          this.reportStore.recordStageScore('flipping_down', this.flipDownScore, 25, '手掌向下翻转阶段完成')
        } else if (currentPalmUp === null) {
          this.feedback = `请保持手在画面中，准备翻转`
        } else {
          this.feedback = `请翻转手掌到掌心向下（拇指移到${this.side === 'left' ? '食指左边' : '食指右边'}）`
        }
        break

      case 'PALM_DOWN_DETECTED':
        // 确认并稳定掌心向下状态
        if (currentPalmUp === false) {
          this.stableFrameCount++
          if (this.stableFrameCount >= this.config.stabilityFrames) {
            this.state = 'HOLDING'
            this.holdStartTime = Date.now()
            this.feedback = `翻转确认！请保持掌心向下 ${this.config.holdDuration / 1000} 秒`
          } else {
            this.feedback = `保持掌心向下... ${this.stableFrameCount}/${this.config.stabilityFrames}`
          }
        } else {
          // 如果又翻回去了，回到准备状态
          this.stableFrameCount = 0
          this.state = 'PALM_UP_READY'
          this.feedback = `请重新翻转到掌心向下`
        }
        break

      case 'HOLDING':
        // 检查是否仍保持掌心向下
        if (currentPalmUp === false) {
          // 保持阶段评分 (0-25分)
          const holdingTime = Date.now() - this.holdStartTime
          const holdProgress = Math.min(1, holdingTime / this.config.holdDuration)
          this.holdScore = Math.max(0, Math.min(25, 25 * holdProgress))
          this.score = this.prepareScore + this.flipDownScore + this.holdScore + this.flipBackScore + this.finalScore

          if (holdProgress >= 1) {
            // 保持阶段完成，现在要求翻转回来
            this.state = 'FLIP_BACK_REQUIRED'
            this.feedback = `保持完成！现在请将手掌翻转回掌心向上`
            this.stableFrameCount = 0

            // 记录保持阶段完成
            this.reportStore.recordStageScore('holding', this.holdScore, 25, '保持姿势阶段完成')
          } else {
            this.feedback = `保持掌心向下... 剩余 ${Math.ceil((this.config.holdDuration - holdingTime) / 1000)} 秒`
          }
        } else {
          // 如果手掌朝向改变，回到检测状态
          this.state = 'PALM_DOWN_DETECTED'
          this.stableFrameCount = 0
          this.feedback = `请保持掌心向下不要翻转`
        }
        break

      case 'FLIP_BACK_REQUIRED':
        // 等待翻转回掌心向上
        if (currentPalmUp === true) {
          this.state = 'FLIP_BACK_DETECTED'
          this.flipBackScore = 25  // 翻转回来阶段完成
          this.score = this.prepareScore + this.flipDownScore + this.holdScore + this.flipBackScore + this.finalScore
          this.feedback = `翻转回来成功！检测到掌心向上，请保持稳定`
          this.stableFrameCount = 0

          // 记录翻转回来阶段完成
          this.reportStore.recordStageScore('flipping_back', this.flipBackScore, 25, '手掌翻转回来阶段完成')
        } else if (currentPalmUp === null) {
          this.feedback = `请保持手在画面中，准备翻转回掌心向上`
        } else {
          this.feedback = `请将手掌翻转回掌心向上（拇指移到${this.side === 'left' ? '食指右边' : '食指左边'}）`
        }
        break

      case 'FLIP_BACK_DETECTED':
        // 确认并稳定掌心向上状态
        if (currentPalmUp === true) {
          this.stableFrameCount++
          if (this.stableFrameCount >= this.config.stabilityFrames) {
            this.state = 'COMPLETED'
            this.finalScore = 5  // 最终确认完成
            this.score = this.prepareScore + this.flipDownScore + this.holdScore + this.flipBackScore + this.finalScore
            this.feedback = `手掌翻转动作完成！最终得分: ${Math.round(this.score)}`

            // 记录最终确认完成
            this.reportStore.recordStageScore('final_confirmation', this.finalScore, 5, '最终确认阶段完成')
          } else {
            this.feedback = `保持掌心向上稳定... ${this.stableFrameCount}/${this.config.stabilityFrames}`
          }
        } else {
          // 如果又翻回去了，回到要求翻转回来状态
          this.stableFrameCount = 0
          this.state = 'FLIP_BACK_REQUIRED'
          this.feedback = `请重新翻转回掌心向上`
        }
        break
    }

    // 记录实时反馈
    this.reportStore.recordFeedback(this.feedback, Math.round(this.score), this.state)

    return {
      success: true,
      state: this.state,
      score: Math.round(this.score),
      feedback: this.feedback,
      // 额外信息
      isPalmUp: currentPalmUp,
      flipDetected: this.flipDetected,
      stableFrames: this.stableFrameCount
    }
  }
}

export default PalmFlipDetector
