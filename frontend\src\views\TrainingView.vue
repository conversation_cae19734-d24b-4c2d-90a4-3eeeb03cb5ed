<template>
  <div class="h-screen bg-gray-900 text-white flex p-4 gap-4 overflow-hidden">
    <!-- 背景辉光 (保持不变) -->
    <div class="absolute top-[-50%] left-[-10%] w-3/4 h-3/4 bg-blue-500/10 rounded-full blur-3xl animate-pulse-slow -z-10"></div>
    <div class="absolute bottom-[-50%] right-[-10%] w-3/4 h-3/4 bg-purple-600/10 rounded-full blur-3xl animate-pulse-slow-delay -z-10"></div>

    <!-- 左侧信息栏 (保持美化后的样式) -->
    <aside class="w-80 bg-black/20 backdrop-blur-xl border border-white/10 rounded-2xl flex flex-col p-4 space-y-4 animate-fade-in-left">
      <!-- 用户与系统状态 -->
      <div class="bg-black/20 rounded-lg p-4 flex-shrink-0">
        <div v-if="patientStore.currentPatient" class="flex items-center space-x-4 mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <User class="text-2xl" />
          </div>
          <div>
            <p class="font-semibold text-lg">{{ patientStore.currentPatient.patient_name }}</p>
            <p class="text-xs text-gray-400">ID: {{ patientStore.currentPatient.patient_id }}</p>
          </div>
        </div>
        <div :class="['flex items-center p-2 rounded-md text-sm font-medium', getStateStatusClass(workflowStore.currentState)]">
          <div :class="['w-2.5 h-2.5 rounded-full mr-2', getStateIndicatorClass(workflowStore.currentState)]"></div>
          <span>{{ getStateDescription(workflowStore.currentState) }}</span>
        </div>
      </div>
      
      <!-- 训练任务列表 -->
      <div class="flex-1 bg-black/20 rounded-lg p-4 flex flex-col min-h-0">
        <h3 class="font-semibold mb-4 text-lg flex-shrink-0">训练任务</h3>
        <div class="flex-1 overflow-y-auto pr-2 custom-scrollbar">
          <transition-group name="action-list" tag="div" class="space-y-3">
            <div
              v-for="(action, index) in trainingStore.actionList"
              :key="action.action_id || index"
              :class="['p-3 rounded-lg transition-all duration-500 border', getActionItemClass(index)]"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div :class="['w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold', getActionStatusIconClass(index)]">
                    <Check v-if="index < currentActionIndex" />
                    <DArrowRight v-else-if="index === currentActionIndex" />
                    <span v-else>{{ index + 1 }}</span>
                  </div>
                  <span class="font-medium">{{ getActionDisplayName(action) }}</span>
                </div>
                <span class="text-xs font-mono">{{ getSideDisplayName(action.side) }}</span>
              </div>
            </div>
          </transition-group>
        </div>
      </div>

      <!-- 用户训练信息卡片 -->
      <div v-if="patientStore.userInfo" class="bg-black/20 rounded-lg p-4 flex-shrink-0">
        <h3 class="font-semibold mb-3 text-sm text-gray-300">训练信息</h3>
        <div class="space-y-3">
          <!-- 患者基本信息 -->
          <div class="flex justify-between items-center">
            <span class="text-xs text-gray-400">患者姓名</span>
            <span class="text-sm font-medium">{{ patientStore.userInfo.patient_name }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-xs text-gray-400">患者ID</span>
            <span class="text-sm font-mono">{{ patientStore.userInfo.patient_id }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-xs text-gray-400">年龄</span>
            <span class="text-sm">{{ patientStore.userInfo.age || '未设置' }}岁</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-xs text-gray-400">性别</span>
            <span class="text-sm">{{ patientStore.userInfo.gender === 'male' ? '男' : patientStore.userInfo.gender === 'female' ? '女' : '未设置' }}</span>
          </div>

          <!-- 训练进度信息 -->
          <div class="border-t border-gray-600 pt-3 mt-3">
            <div class="flex justify-between items-center">
              <span class="text-xs text-gray-400">训练进度</span>
              <span class="text-sm">{{ currentActionIndex + 1 }}/{{ trainingStore.actionList.length }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-xs text-gray-400">当前得分</span>
              <span class="text-sm font-bold" :class="currentScore >= 80 ? 'text-green-400' : currentScore >= 60 ? 'text-yellow-400' : 'text-blue-400'">
                {{ currentScore }}分
              </span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-xs text-gray-400">训练状态</span>
              <span class="text-sm" :class="workflowStore.currentState === 'training' ? 'text-green-400' : 'text-yellow-400'">
                {{ getStateDescription(workflowStore.currentState) }}
              </span>
            </div>
          </div>

          <!-- 连接状态 -->
          <div class="border-t border-gray-600 pt-3 mt-3">
            <div class="flex justify-between items-center">
              <span class="text-xs text-gray-400">摄像头状态</span>
              <div class="flex items-center space-x-1">
                <div :class="['w-2 h-2 rounded-full', connectionStore.isConnected ? 'bg-green-400' : 'bg-red-400']"></div>
                <span class="text-xs">{{ connectionStore.isConnected ? '已连接' : '未连接' }}</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-xs text-gray-400">训练会话</span>
              <div class="flex items-center space-x-1">
                <div :class="['w-2 h-2 rounded-full', trainingSession.isSessionActive.value ? 'bg-green-400 animate-pulse' : 'bg-gray-400']"></div>
                <span class="text-xs">{{ trainingSession.isSessionActive.value ? '进行中' : '未开始' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </aside>

    <!-- 右侧主训练区 【核心布局修正】 -->
    <main class="flex-1 grid grid-cols-2 grid-rows-2 gap-4 animate-fade-in-right">
      <!-- 右侧跨两行：实时画面 -->
      <div class="col-span-1 row-span-2 bg-black/20 backdrop-blur-xl border border-white/10 rounded-2xl flex flex-col overflow-hidden">
        <div class="p-3 border-b border-white/10 flex-shrink-0">
          <h3 class="font-semibold text-center">实时画面</h3>
        </div>
        <div class="flex-1 relative bg-black/30">
          <VideoStream ref="videoStreamComponentRef" />
          <PoseOverlay :target-ref="videoStreamComponentRef" />
          <div v-if="workflowStore.isPaused" class="absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-20 text-2xl font-bold">
            训练已暂停
          </div>
        </div>
      </div>
      <!-- 左上：标准动作演示 -->
      <div class="col-span-1 row-span-1 bg-black/20 backdrop-blur-xl border border-white/10 rounded-2xl flex flex-col overflow-hidden">
        <div class="p-3 border-b border-white/10 flex-shrink-0">
          <h3 class="font-semibold text-center">标准动作演示</h3>
        </div>
        <div class="flex-1 relative bg-black/30">
          <video
            v-if="videoUrl"
            :src="videoUrl"
            ref="standardVideoRef"
            class="w-full h-full object-contain"
            loop
            autoplay
            :volume="videoVolume"
            @loadedmetadata="handleStandardVideoLoaded"
          ></video>
          <div v-else class="flex items-center justify-center h-full text-gray-500"><p>暂无演示视频</p></div>
        </div>
      </div>
      
      <!-- 左下：增强实时反馈 -->
      <div class="col-span-1 row-span-1 bg-black/20 backdrop-blur-xl border border-white/10 rounded-2xl flex flex-col overflow-hidden p-4">
        <h3 class="font-semibold text-center mb-3 text-lg">实时反馈</h3>

        <!-- 综合得分仪表盘 -->
        <div class="flex items-center justify-center mt-10">
          <div class="relative w-32 h-32">
            <svg class="w-full h-full" viewBox="0 0 100 100">
              <circle class="text-gray-700" stroke-width="6" stroke="currentColor" fill="transparent" r="40" cx="50" cy="50" />
              <circle
                class="transition-all duration-500 ease-out"
                :class="currentScore >= 80 ? 'text-green-400' : currentScore >= 60 ? 'text-yellow-400' : 'text-blue-400'"
                stroke-width="6"
                stroke-linecap="round" stroke="currentColor" fill="transparent"
                r="40" cx="50" cy="50" transform="rotate(-90 50 50)"
                :stroke-dasharray="`${2.51 * currentScore} 251`"
              />
            </svg>
            <div class="absolute inset-0 flex flex-col items-center justify-center">
              <span class="text-2xl font-bold">{{ currentScore }}</span>
              <span class="text-xs text-gray-400">分数</span>
            </div>
          </div>
        </div>
        <!-- 智能反馈信息 -->
        <div class="flex-1 flex flex-col justify-center space-y-3">
          <!-- 动作阶段指示器 -->
          <div v-if="currentActionStage" class="text-center">
            <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-all duration-300"
                 :class="getActionStageStyle(currentActionStage)">
              <span class="mr-2">{{ getActionStageIcon(currentActionStage) }}</span>
              {{ getActionStageText(currentActionStage) }}
            </div>
          </div>
          <!-- 实时反馈文本 -->
          <div v-if="currentFeedback" class="text-center px-2">
            <p class="text-4xl text-green-300 leading-relaxed">{{ currentFeedback }}</p>
          </div>
        </div>
      </div>
    </main>
    <!-- 动作介绍模态框  -->
    <Transition name="modal">
      <div v-if="showActionIntroDialog" class="fixed inset-0 bg-black/70 backdrop-blur-md z-50 flex items-center justify-center p-4 sm:p-8">
        <div class="w-full max-w-6xl bg-gray-900/80 border border-white/10 rounded-2xl shadow-2xl flex flex-col overflow-hidden animate-zoom-in">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 p-10">
            <!-- 左侧视频 -->
            <div class="flex flex-col">
              <!-- 【改动点 3】: 增大标题字号 -->
              <h2 class="text-3xl font-bold mb-4 text-center">动作演示</h2>
              <div class="flex-1 bg-black rounded-lg overflow-hidden aspect-video shadow-lg">
                <video
                  v-if="introVideoUrl"
                  :src="introVideoUrl"
                  ref="introVideoRef"
                  class="w-full h-full object-contain"
                  autoplay
                  muted
                  @ended="handleIntroVideoEnded"
                ></video>
                <div v-else class="h-full flex items-center justify-center text-gray-500">暂无视频</div>
              </div>
            </div>
            <!-- 右侧信息 -->
            <div class="flex flex-col justify-center">
              <h2 class="text-4xl font-bold mb-6">{{ getActionDisplayName(trainingStore.currentAction) }}</h2>
              <div class="space-y-6 text-lg text-gray-300">
                <div class="flex items-center">
                  <strong class="font-semibold text-white w-24 flex-shrink-0">侧别:</strong>
                  <span>{{ getSideDisplayName(trainingStore.currentAction?.side) }}</span>
                </div>
                <div class="flex items-center">
                  <strong class="font-semibold text-white w-24 flex-shrink-0">难度:</strong>
                  <span :class="[
                    'px-3 py-1 text-sm rounded-full',
                    getDifficultyInfo(trainingStore.currentAction?.difficulty_level).colorClass.replace('/20', '/30') // 背景色更深一点
                  ]">
                    {{ getDifficultyInfo(trainingStore.currentAction?.difficulty_level).text }}
                  </span>
                </div>
                <div class="max-h-48 overflow-y-auto custom-scrollbar pr-2">
                  <strong class="font-semibold text-white block mb-2">动作要点:</strong>
                  <p class="leading-relaxed">{{ getActionDescription(trainingStore.currentAction) }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="p-5 bg-black/20 border-t border-white/10 mt-auto">
            <div class="flex items-center justify-between">
              <p class="text-base text-gray-400">视频播放完成后将自动开始</p>
              <button @click="handleManualCloseIntro" class="px-8 py-3 bg-blue-600 hover:bg-blue-500 rounded-lg font-semibold transition-colors text-base">
                我已了解，开始训练
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
    <Transition name="modal">
      <div
        v-if="showPauseDialog"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-xl"
      >
        <div class="bg-white/10 border border-white/20 rounded-2xl p-10 shadow-2xl text-center text-white max-w-lg w-full">
          <h2 class="text-3xl font-bold mb-4">训练已暂停</h2>
          <p class="mb-6 text-lg">未检测到您的身份或您已离开画面，请返回摄像头前继续训练。</p>
          <div class="text-gray-300 text-sm">请确保摄像头正常，且您在画面中。</div>
        </div>
      </div>
    </Transition>
    <!-- 庆祝动画 -->
    <CelebrationAnimation
      :is-visible="notificationStore.showCelebrationAnimation"
      :action-name="notificationStore.celebrationData?.actionName || ''"
      :score="notificationStore.celebrationData?.score || 0"
      :current-action-index="notificationStore.celebrationData?.currentActionIndex || 0"
      :total-actions="notificationStore.celebrationData?.totalActions || 1"
      :next-action-name="notificationStore.celebrationData?.nextActionName || ''"
      :duration="notificationStore.celebrationData?.duration || 3000"
      @complete="handleCelebrationComplete"
      @skip="handleCelebrationSkip"
    />
  </div>
</template>
<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useTrainingStore } from '@/stores/training';
import { useWorkflowStore } from '@/stores/workflow';
import { usePatientStore } from '@/stores/patient';
import { useConnectionStore } from '@/stores/connection';
import { useNotificationStore } from '@/stores/notification';
import { useStateTransition } from '@/composables/useStateTransition';
import { useEnhancedTrainingSession } from '@/composables/useEnhancedTrainingSession';
import { useAudioFeedback } from '@/composables/useAudioFeedback';
import { User, Check, DArrowRight } from '@element-plus/icons-vue';
import VideoStream from '@/components/VideoStream.vue';
import PoseOverlay from '@/components/PoseOverlay.vue';
import CelebrationAnimation from '@/components/CelebrationAnimation.vue';

const trainingStore = useTrainingStore();
const workflowStore = useWorkflowStore();
const patientStore = usePatientStore();
const connectionStore = useConnectionStore();
const notificationStore = useNotificationStore();

const stateTransition = useStateTransition();
const trainingSession = useEnhancedTrainingSession();
const audioFeedback = useAudioFeedback();

// --- UI状态 ---
const showActionIntroDialog = ref(false);
const introVideoUrl = ref(''); // 弹窗中的视频URL
const videoUrl = ref(''); // 训练页标准视频URL

// --- 音频视频协调控制 ---
const videoVolume = ref(0.6); // 默认视频音量
const normalVideoVolume = 0.6; // 正常视频音量
const reducedVideoVolume = 0.1; // 语音播报时的降低音量

// --- Template Refs ---
const videoStreamComponentRef = ref(null);
const standardVideoRef = ref(null);
const introVideoRef = ref(null);
// --- 计算属性 ---
const currentActionIndex = computed(() => {
  if (!trainingStore.currentAction || !trainingStore.actionList.length) return -1;
  // 确保使用唯一的标识符进行查找
  const idKey = trainingStore.actionList[0]?.action_id ? 'action_id' : 'action_type';
  return trainingStore.actionList.findIndex(a => a[idKey] === trainingStore.currentAction[idKey]);
});

const getStateStatusClass = (state) => ({ 'training': 'bg-green-500/20 text-green-300', 'preparation': 'bg-yellow-500/20 text-yellow-300' }[state] || 'bg-gray-500/20 text-gray-300');
const getStateIndicatorClass = (state) => ({ 'training': 'bg-green-400 animate-pulse', 'preparation': 'bg-yellow-400' }[state] || 'bg-gray-400');
const getStateDescription = (state) => ({ 'training': '训练进行中', 'preparation': '准备新动作', 'pause': '训练已暂停', 'introduction': '任务介绍中' }[state] || '等待指令');

const getDifficultyInfo = (level) => {
  const defaultInfo = { text: '常规', colorClass: 'bg-gray-500/20 text-gray-300' };
  if (!level) return defaultInfo;
  
  switch (level.toLowerCase()) {
    case 'easy':
      return { text: '简单', colorClass: 'bg-green-500/20 text-green-300' };
    case 'medium':
      return { text: '中等', colorClass: 'bg-yellow-500/20 text-yellow-300' };
    case 'hard':
      return { text: '困难', colorClass: 'bg-red-500/20 text-red-300' };
    default:
      return defaultInfo;
  }
};
const getActionDisplayName = (action) => action?.action_type?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || '未知动作';
const getSideDisplayName = (side) => ({ 'left': '左侧', 'right': '右侧' }[side] || '双侧');
const getActionDescription = (action) => action?.description || '暂无详细描述，请参考标准视频。';

const getActionItemClass = (index) => {
  if (index === currentActionIndex.value) return 'border-blue-500 bg-blue-500/20 scale-105 shadow-lg';
  if (index < currentActionIndex.value) return 'border-transparent bg-transparent opacity-50';
  return 'border-transparent bg-transparent opacity-80';
};
const getActionStatusIconClass = (index) => {
  if (index === currentActionIndex.value) return 'bg-blue-500 text-white';
  if (index < currentActionIndex.value) return 'bg-green-500/80 text-white';
  return 'bg-gray-600 text-gray-300';
};

// --- 视频音频控制方法 ---
const handleStandardVideoLoaded = () => {
  console.log('[TrainingView] 标准视频加载完成，初始音量:', videoVolume.value)
  // 设置视频初始音量
  if (standardVideoRef.value) {
    standardVideoRef.value.volume = videoVolume.value
  }
};

// 监听音频播放状态，动态调整视频音量
watch(
  () => audioFeedback.isPlayingAudio.value,
  (isPlaying) => {
    console.log('[TrainingView] 音频播放状态变化:', isPlaying)
    if (isPlaying) {
      // 语音播报开始，降低视频音量
      videoVolume.value = reducedVideoVolume
    } else {
      // 语音播报结束，恢复视频音量
      videoVolume.value = normalVideoVolume
    }

    // 立即应用到视频元素
    if (standardVideoRef.value) {
      standardVideoRef.value.volume = videoVolume.value
      console.log('[TrainingView] 视频音量已调整为:', videoVolume.value)
    }
  },
  { immediate: true }
);

// --- 动作阶段相关方法 ---
const currentActionStage = computed(() => trainingSession.currentActionStage)
const currentScore = computed(() => trainingSession.currentScore)
const currentFeedback = computed(() => trainingSession.currentFeedback)

const getActionStageStyle = (stage) => {
  const styles = {
    'waiting': 'bg-gray-500/20 text-gray-300',
    'moving': 'bg-blue-500/20 text-blue-300',
    'holding': 'bg-yellow-500/20 text-yellow-300',
    'returning': 'bg-purple-500/20 text-purple-300',
    'completed': 'bg-green-500/20 text-green-300',
    'error': 'bg-red-500/20 text-red-300'
  }
  return styles[stage] || styles['waiting']
}

const getActionStageIcon = (stage) => {
  const icons = {
    'waiting': '⏳',
    'moving': '🎯',
    'holding': '⏱️',
    'returning': '↩️',
    'completed': '✅',
    'error': '❌'
  }
  return icons[stage] || '⏳'
}

const getActionStageText = (stage) => {
  const texts = {
    'waiting': '等待开始',
    'moving': '移动中',
    'holding': '保持中',
    'returning': '返回中',
    'completed': '已完成',
    'error': '出现错误'
  }
  return texts[stage] || '等待开始'
}

// --- 事件处理器 ---
const closeIntroAndStart = () => {
  if (!showActionIntroDialog.value) return; // 防止重复调用
  showActionIntroDialog.value = false;
  console.log('介绍结束，转换到训练状态');
  stateTransition.handleActionIntroComplete();
};

const handleManualCloseIntro = () => {
  closeIntroAndStart();
};
const handleIntroVideoEnded = () => {
  setTimeout(() => closeIntroAndStart(), 500);
};

// 庆祝动画事件处理
const handleCelebrationComplete = () => {
  console.log('[TrainingView] 庆祝动画完成')
  notificationStore.hideCelebrationAnimation()
  // 动画完成后继续原有的状态转换逻辑
  // 这里不需要额外处理，因为状态转换已经在 handleActionComplete 中设置了定时器
}

const handleCelebrationSkip = () => {
  console.log('[TrainingView] 跳过庆祝动画')
  notificationStore.hideCelebrationAnimation()
  
}
// 【核心逻辑修正】使用一个专门的函数来触发弹窗
const triggerIntroDialog = (action) => {
  if (action && action.video_url) {
    introVideoUrl.value = action.video_url;
  } else {
    // 如果没有视频，也显示弹窗，但给个默认提示
    introVideoUrl.value = '';
    console.warn(`动作 ${action?.action_type} 没有提供 video_url`);
  }
  // 更新主训练区的标准视频
  videoUrl.value = action?.video_url || '';
  // 显示弹窗
  showActionIntroDialog.value = true;
  // 播放视频
  nextTick(() => {
    introVideoRef.value?.play().catch(e => console.error("介绍视频自动播放失败:", e));
  });
};
watch(
  () => [workflowStore.currentState, trainingStore.currentAction],
  // 【核心修正】为解构的参数提供默认的空数组 []
  ([newState, newAction], [oldState, oldAction] = []) => {
    // 关键条件：只有当状态是 'preparation' 时，我们才考虑弹窗
    if (newState === 'preparation') {
      // 两种情况需要弹窗：
      // 1. 刚刚进入 preparation 状态 (例如从 introduction 切换过来)
      // 2. 已经在 preparation 状态，但动作更新了 (action_id 或其他唯一标识)
      // 使用可选链 (?.) 增加代码健壮性
      if (newAction && (newState !== oldState || newAction?.action_id !== oldAction?.action_id)) {
        console.log(`检测到准备新动作: ${newAction.action_type}，准备弹出介绍框。`);
        triggerIntroDialog(newAction);
      }
    } else {
      // 如果状态不是 'preparation'，确保弹窗是关闭的
      if (showActionIntroDialog.value) {
        showActionIntroDialog.value = false;
      }
      // 并且，如果不在准备阶段，也应该更新主视频区
      if (newAction) {
          videoUrl.value = newAction.video_url || '';
      }
    }
  },
  { deep: true, immediate: true } // immediate: true 确保组件加载时立即执行一次检查
);

// 监听训练状态变化
watch(
  () => workflowStore.currentState,
  (newState, oldState) => {
    console.log(`[TrainingView] 状态变化: ${oldState} -> ${newState}`)
    console.log(`[TrainingView] 当前动作:`, trainingStore.currentAction)
    console.log(`[TrainingView] 训练会话状态:`, trainingSession.isSessionActive.value)

    if (newState === 'training') {
      // 检查当前动作是否存在
      if (!trainingStore.currentAction) {
        console.warn('[TrainingView] 警告：进入训练状态但没有当前动作，尝试初始化')
        trainingStore.initializeActions()
      }
      // 确保训练会话已启动（useEnhancedTrainingSession会自动处理，但我们添加日志确认）
      console.log('[TrainingView] 进入训练状态，训练会话应该自动启动')
      // 如果训练会话没有自动启动，手动启动
      if (!trainingSession.isSessionActive.value) {
        console.warn('[TrainingView] 训练会话未自动启动，手动启动')
        trainingSession.startSession()
      }
    }
  }
)

const showPauseDialog = ref(false); // 玻璃风格全屏弹窗
let patientIdTimeout = null;

watch(
  () => connectionStore.patientId,
  (newId) => {
    // 先清掉上一次的定时器
    if (patientIdTimeout) {
      clearTimeout(patientIdTimeout);
      patientIdTimeout = null;
    }
    // 判断是否为当前登录用户
    const loginId = patientStore.userInfo?.patient_id;
    if (newId && loginId && newId === loginId) {
      // 恢复训练
      showPauseDialog.value = false;
      if (workflowStore.isPaused) {
        workflowStore.resumeWorkflow(); // 你需要有 resume 方法
      }
    } else {
      // 3秒内没检测到，弹窗暂停
      patientIdTimeout = setTimeout(() => {
        showPauseDialog.value = true;
        if (!workflowStore.isPaused) {
          workflowStore.pauseWorkflow(); // 你需要有 pause 方法
        }
      }, 3000);
    }
  },
  { immediate: true }
);
// 组件挂载时启动增强训练会话
onMounted(() => {
  console.log('[TrainingView] 组件挂载')
  console.log('[TrainingView] 当前状态:', workflowStore.currentState)
  console.log('[TrainingView] 当前动作:', trainingStore.currentAction)
  console.log('[TrainingView] 训练会话状态:', trainingSession.isSessionActive.value)

  // 如果当前状态是training，确保训练会话已启动
  if (workflowStore.currentState === 'training') {
    console.log('[TrainingView] 当前处于训练状态，检查训练会话')
    if (!trainingSession.isSessionActive.value && trainingStore.currentAction) {
      console.log('[TrainingView] 手动启动训练会话')
      trainingSession.startSession()
    }
  }
})
// 确保组件卸载时清理资源
onUnmounted(() => {
  console.log('[TrainingView] 组件卸载，清理资源')

  // 清理定时器
  if (patientIdTimeout) {
    clearTimeout(patientIdTimeout);
    patientIdTimeout = null;
  }

  // 如果训练会话仍然活跃，结束会话
  if (trainingSession.isSessionActive.value) {
    console.log('[TrainingView] 结束训练会话')
    trainingSession.endSession()
  }
});
</script>

<style scoped>
/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}
.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

/* 介绍弹窗动画 */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.4s ease;
}
.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}
.modal-enter-active .animate-zoom-in,
.modal-leave-active .animate-zoom-in {
  transition: all 0.4s ease;
}
.modal-enter-from .animate-zoom-in {
  transform: scale(0.9);
  opacity: 0;
}
.modal-leave-to .animate-zoom-in {
  transform: scale(0.9);
  opacity: 0;
}

/* 任务列表切换动画 */
.action-list-move,
.action-list-enter-active,
.action-list-leave-active {
  transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
}
.action-list-enter-from,
.action-list-leave-to {
  opacity: 0;
  transform: scale(0.9) translateX(30px);
}
.action-list-leave-active {
  position: absolute;
}

/* 准备就绪动画 */
.ready-pulse-enter-active,
.ready-pulse-leave-active {
  transition: all 0.5s ease-in-out;
}

.ready-pulse-enter-from {
  opacity: 0;
  transform: scale(0.8);
}

.ready-pulse-leave-to {
  opacity: 0;
  transform: scale(1.1);
}

/* 背景辉光动画 */
@keyframes pulse-slow {
  50% { opacity: 0.15; }
}
@keyframes pulse-slow-delay {
  50% { opacity: 0.15; }
}
.animate-pulse-slow { animation: pulse-slow 8s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-pulse-slow-delay { animation: pulse-slow 8s cubic-bezier(0.4, 0, 0.6, 1) infinite 4s; }

/* 页面进入动画 */
@keyframes fade-in-left { from { opacity: 0; transform: translateX(-20px); } to { opacity: 1; transform: translateX(0); } }
@keyframes fade-in-right { from { opacity: 0; transform: translateX(20px); } to { opacity: 1; transform: translateX(0); } }
.animate-fade-in-left { animation: fade-in-left 0.7s 0.1s ease-out forwards; }
.animate-fade-in-right { animation: fade-in-right 0.7s 0.3s ease-out forwards; }
</style>