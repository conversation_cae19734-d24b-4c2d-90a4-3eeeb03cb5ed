/**
 * 无接触异常处理系统测试辅助工具
 * 用于验证所有无接触异常处理组件的集成和功能
 */

export class TouchlessTestHelper {
  constructor() {
    this.testResults = [];
    this.isTestMode = false;
  }

  /**
   * 启动测试模式
   */
  startTestMode() {
    this.isTestMode = true;
    this.testResults = [];
    console.log("[TouchlessTestHelper] 测试模式已启动");
  }

  /**
   * 停止测试模式
   */
  stopTestMode() {
    this.isTestMode = false;
    console.log("[TouchlessTestHelper] 测试模式已停止");
    this.printTestResults();
  }

  /**
   * 记录测试结果
   */
  recordTest(testName, passed, details = "") {
    const result = {
      name: testName,
      passed,
      details,
      timestamp: new Date().toISOString(),
    };
    this.testResults.push(result);

    const status = passed ? "✅ PASS" : "❌ FAIL";
    console.log(`[TouchlessTestHelper] ${status}: ${testName}`, details);
  }

  /**
   * 打印测试结果摘要
   */
  printTestResults() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter((r) => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log("\n=== 无接触异常处理系统测试结果 ===");
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(
      `成功率: ${
        totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0
      }%`
    );

    if (failedTests > 0) {
      console.log("\n失败的测试:");
      this.testResults
        .filter((r) => !r.passed)
        .forEach((r) => console.log(`  - ${r.name}: ${r.details}`));
    }
    console.log("=====================================\n");
  }

  /**
   * 测试超时检测系统
   */
  async testTimeoutDetection(touchlessTimeout) {
    if (!this.isTestMode) return;

    try {
      // 测试启动检测
      const mockAction = {
        action_id: "test_1",
        action_name: "测试动作",
        action_type: "test_action",
      };

      const callbacks = {
        onTimeout: (data) =>
          this.recordTest(
            "超时回调触发",
            true,
            `超时数据: ${JSON.stringify(data)}`
          ),
        onWarning: (data) =>
          this.recordTest(
            "警告回调触发",
            true,
            `警告数据: ${JSON.stringify(data)}`
          ),
        onStagnation: (data) =>
          this.recordTest(
            "停滞回调触发",
            true,
            `停滞数据: ${JSON.stringify(data)}`
          ),
      };

      touchlessTimeout.startDetection(mockAction, callbacks);
      this.recordTest(
        "超时检测启动",
        touchlessTimeout.isDetectionActive.value,
        "检测状态应为活跃"
      );

      // 测试进度更新
      touchlessTimeout.updateProgress("MOVING_TO_TARGET", 0.5);
      this.recordTest("进度更新", true, "进度更新无异常");

      // 测试停止检测
      touchlessTimeout.stopDetection();
      this.recordTest(
        "超时检测停止",
        !touchlessTimeout.isDetectionActive.value,
        "检测状态应为非活跃"
      );
    } catch (error) {
      this.recordTest("超时检测系统", false, `错误: ${error.message}`);
    }
  }

  /**
   * 测试身份验证系统
   */
  async testIdentityVerification(enhancedIdentity) {
    if (!this.isTestMode) return;

    try {
      const mockPatientId = "test_patient_123";
      const mockStage = "training";

      const callbacks = {
        onIdentityLost: (data) =>
          this.recordTest(
            "身份丢失回调",
            true,
            `身份数据: ${JSON.stringify(data)}`
          ),
        onUnauthorizedPerson: (data) =>
          this.recordTest(
            "未授权人员回调",
            true,
            `身份数据: ${JSON.stringify(data)}`
          ),
        onMultiplePersons: (data) =>
          this.recordTest(
            "多人检测回调",
            true,
            `身份数据: ${JSON.stringify(data)}`
          ),
        onIdentityRecovered: () =>
          this.recordTest("身份恢复回调", true, "身份验证恢复"),
      };

      enhancedIdentity.startVerification(mockPatientId, mockStage, callbacks);
      this.recordTest(
        "身份验证启动",
        enhancedIdentity.isVerificationActive.value,
        "验证状态应为活跃"
      );

      // 测试姿态数据更新
      const mockPoseData = Array(133)
        .fill()
        .map((_, i) => ({ x: i, y: i, confidence: 0.8 }));
      enhancedIdentity.updatePoseData(mockPoseData, mockPatientId);
      this.recordTest("姿态数据更新", true, "姿态数据更新无异常");

      // 测试患者ID更新
      enhancedIdentity.updatePatientId("different_patient");
      this.recordTest("患者ID更新", true, "患者ID更新无异常");

      // 测试停止验证
      enhancedIdentity.stopVerification();
      this.recordTest(
        "身份验证停止",
        !enhancedIdentity.isVerificationActive.value,
        "验证状态应为非活跃"
      );
    } catch (error) {
      this.recordTest("身份验证系统", false, `错误: ${error.message}`);
    }
  }

  /**
   * 测试紧急手势检测
   */
  async testEmergencyGestures(emergencyGestures) {
    if (!this.isTestMode) return;

    try {
      const callbacks = {
        onEmergencyStop: (data) =>
          this.recordTest(
            "紧急停止手势",
            true,
            `手势数据: ${JSON.stringify(data)}`
          ),
        onRequestHelp: (data) =>
          this.recordTest(
            "求助手势",
            true,
            `手势数据: ${JSON.stringify(data)}`
          ),
        onRestartTraining: (data) =>
          this.recordTest(
            "重启手势",
            true,
            `手势数据: ${JSON.stringify(data)}`
          ),
      };

      emergencyGestures.startDetection(callbacks);
      this.recordTest(
        "紧急手势检测启动",
        emergencyGestures.isDetectionActive.value,
        "检测状态应为活跃"
      );

      // 测试姿态数据更新
      const mockPoseData = Array(133)
        .fill()
        .map((_, i) => ({ x: i, y: i, confidence: 0.8 }));
      emergencyGestures.updatePoseData(mockPoseData);
      this.recordTest("手势姿态数据更新", true, "姿态数据更新无异常");

      // 测试停止检测
      emergencyGestures.stopDetection();
      this.recordTest(
        "紧急手势检测停止",
        !emergencyGestures.isDetectionActive.value,
        "检测状态应为非活跃"
      );
    } catch (error) {
      this.recordTest("紧急手势检测系统", false, `错误: ${error.message}`);
    }
  }

  /**
   * 测试异常状态管理
   */
  async testExceptionStore(touchlessExceptionStore) {
    if (!this.isTestMode) return;

    try {
      // 测试记录跳过
      const skipData = {
        actionId: "test_action_1",
        reason: "timeout",
        timestamp: Date.now(),
      };

      touchlessExceptionStore.recordSkip(skipData);
      this.recordTest(
        "记录动作跳过",
        touchlessExceptionStore.skipRecords.length > 0,
        "跳过记录应增加"
      );

      // 测试记录警告
      const warningData = {
        type: "timeout_warning",
        message: "测试警告",
        timestamp: Date.now(),
      };

      touchlessExceptionStore.recordWarning(warningData);
      this.recordTest(
        "记录警告",
        touchlessExceptionStore.warningRecords.length > 0,
        "警告记录应增加"
      );

      // 测试设置当前异常
      const exceptionData = {
        type: "identity_exception",
        reason: "unauthorized_person",
        timestamp: Date.now(),
      };

      touchlessExceptionStore.setCurrentException(exceptionData);
      this.recordTest(
        "设置当前异常",
        touchlessExceptionStore.currentException !== null,
        "当前异常应被设置"
      );

      // 测试清理异常
      touchlessExceptionStore.clearCurrentException();
      this.recordTest(
        "清理当前异常",
        touchlessExceptionStore.currentException === null,
        "当前异常应被清理"
      );

      // 测试获取统计信息
      const stats = touchlessExceptionStore.getStatistics();
      this.recordTest(
        "获取统计信息",
        typeof stats === "object",
        "统计信息应为对象"
      );
    } catch (error) {
      this.recordTest("异常状态管理", false, `错误: ${error.message}`);
    }
  }

  /**
   * 运行完整的集成测试
   */
  async runFullIntegrationTest(systems) {
    console.log("[TouchlessTestHelper] 开始完整集成测试...");
    this.startTestMode();

    const {
      touchlessTimeout,
      enhancedIdentity,
      emergencyGestures,
      touchlessExceptionStore,
    } = systems;

    // 依次测试各个系统
    await this.testTimeoutDetection(touchlessTimeout);
    await this.testIdentityVerification(enhancedIdentity);
    await this.testEmergencyGestures(emergencyGestures);
    await this.testExceptionStore(touchlessExceptionStore);

    this.stopTestMode();
    return this.testResults;
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    return {
      summary: {
        total: this.testResults.length,
        passed: this.testResults.filter((r) => r.passed).length,
        failed: this.testResults.filter((r) => !r.passed).length,
        successRate:
          this.testResults.length > 0
            ? (
                (this.testResults.filter((r) => r.passed).length /
                  this.testResults.length) *
                100
              ).toFixed(1)
            : 0,
      },
      details: this.testResults,
      timestamp: new Date().toISOString(),
    };
  }
}

// 创建全局测试实例
export const touchlessTestHelper = new TouchlessTestHelper();

// 在开发环境下暴露到全局
if (import.meta.env.DEV) {
  window.touchlessTestHelper = touchlessTestHelper;
}
